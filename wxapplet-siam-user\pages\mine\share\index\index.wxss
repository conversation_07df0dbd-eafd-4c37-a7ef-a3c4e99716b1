page {
  background: #feefd0;
}

.share-top{
  text-align: center;
}

.share-text-top{
  font-size:40rpx;
  font-weight: bold;
  padding-bottom: 20rpx;
  background: white;
}

.wodejl-view{
  position: fixed;
  margin-top: 6%;
  right: 0;
  background: linear-gradient(to right, #ff5c57 , #fe4646);
  padding: 10rpx 20rpx;
  border-radius: 40rpx 0 0 40rpx;
  color: white;
  font-size: 30rpx;
}

/* 关于背景图的样式 */
.top-img {
  height: 159rpx;
  width: 100%;
  margin-top: 130rpx;
}

.top-coupon{
  /* margin-top: -9%; */
  width: 100%;
  text-align: center;
}

.hdgz-view{
  color: #929392;
  font-size: 28rpx;
  z-index: 9999999999;
}

.top-list{
  width: 100%;
  text-align: center;
  z-index: 9999;
  margin-top: 5%;
}

.top-coupon-img{
  height: 740rpx;
  width: 100%;
}

.top-tis{
  margin-top: 50%;
  position: absolute;
  text-align: center;
  width: 100%;
  font-size: 32rpx;
  color: #6e6e6e;
}

.top-send{
  margin-top: -37%;
}

button{
  background: inherit;
}

button:after{
  border:none;
}

.top-send-img{
  width: 46%;
}

.top-list-img{
  width: 82%;
  height: 740rpx;
}

.top-null-view{
  margin-top: -10%;
  z-index: 99999;
}

.top-null-img{
  width: 100%;
}

.invite-relation-number{
  margin-top: -65.5%;
  z-index: 99999;
  margin-left: 12%;
  color: white;
}

/* 邀请记录区块 */
.invite-all-wrapper {
  margin-bottom: 130px;
}
.title {
  text-align: center;
  font-size: 12px;
  margin-top: 15px;
  /* color: white; */
  font-weight: bold;
}
.invite-relation-wrapper{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx;
}
/* .invite-relation-wrapper:first-child{
  padding-top: 16rpx;
}
.invite-relation-wrapper:last-child{
  padding-bottom: 16rpx;
} */
.list-wrapper {
  z-index: 99999;
  color: white;
  margin: 0 15%;
  width: 70%;
  /* padding: 16rpx 26rpx;   */
}
.user-info-wrapper {
  display: flex;
  justify-content: flex-start;
  justify-content: space-between;
  align-items: center;
  width: 80%;
}
.headImg {
  width: 38px;
  height: 38px;
  border-radius: 50%;
}
.username {
  margin-left: 10px;
  width: 70%;
}

.data-emtpy-description{
  text-align: center;
  font-size: 12px;
  margin-top: 15px;
  margin-bottom: 15px;
  /* color: white; */
  font-weight: bold;
}
.number-text {
  /* 金麒麟色 */
  color: #DAA520;
  font-weight: bold;
}

/* 底部悬浮区块 */
.bottom-wrapper {
  height: 120px;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: column;
  background: white;
  /* Start 底部悬浮控制 */
  position: fixed;
  bottom: 0px;
  width: 100%;  
  /* End 底部悬浮控制 */
}
.wxchat-icon {
  width: 50px;
  height: 50px;
}
.wxchat-icon-description {
  font-size: 13px;
  font-weight: bold;
}
.share-button {
  background-color: unset;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content-class{
  position: absolute;
  top: 0;
  /* margin-top: 6%; */
  width: 100%;
}

/* 自定义弹出框的最大高度为100%，并设置他的左右上交的border-ric为0 */
.weui-show .weui-half-screen-dialog.extClassShoppingCart {
  max-height: 100vh;
  padding: 0 20rpx;
  position: fixed;
  bottom: 0;
}

.weui-half-screen-dialog.extClassShoppingCart .weui-half-screen-dialog__ft {
  padding: 20rpx 0;
  position: sticky;
  bottom: 0;
}


.weui-show .weui-half-screen-dialog.extClassShoppingCart .weui-half-screen-dialog__hd {
  padding: 0 20rpx;
}

/* 自定义弹出框的最大高度为100%，并设置他的左右上交的border-ric为0 */
.weui-show .weui-half-screen-dialog.extClassCreateGoodImg {
  max-height: 100vh;
  padding: 0 20rpx;
  position: fixed;
  bottom: 0;
}

.weui-half-screen-dialog.extClassCreateGoodImg .weui-half-screen-dialog__ft {
  padding: 20rpx 0;
  position: sticky;
  bottom: 0;
}

.weui-show .weui-half-screen-dialog.extClassCreateGoodImg .weui-half-screen-dialog__hd {
  padding: 0 20rpx;
}

.weui-half-screen-dialog.extClassCreateGoodImg{
  border-radius: 0;
}

.activity-rule{
  text-align: left;
  margin-bottom: 10rpx;
  
}

.activity-rule text{
  font-size: 28rpx;
}

.invite-wechat-image{
  width: 120rpx;
  height: auto;
}

.invite-tupian-image{
  width: 140rpx;
  height: auto;
}

.share-text{
  font-size: 30rpx;
}

.carousel-swiper{
  height: 80%;
}

.carousel-show{
  height: 100%;
  padding-bottom: 5%;
}

.carousel-swiper-item{
  border-radius: 15rpx;
  height:100%;
}

.carousel-image{
  width: 100%;
  height:100%;
}

.weui-mask{
  z-index: 5000;
}

.weui-mask, .weui-mask_transparent {
  z-index: 5000;
}

.qrcodeImage{
  width: 100%;
}

.wx-swiper-dots{position:relative;top: 0;text-align: center;}
.wx-swiper-dots.wx-swiper-dots-horizontal{
  margin-top: 15rpx;
}

.halfScreenDialog-btn{
  height: 56rpx;
}

button[type='default'] {
  background-color: inherit;
}
  
<scroll-view scroll-y="true">
	<swiper indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" class="carousel-swiper"
	interval="{{interval}}" duration="{{duration}}"
		indicator-active-color="{{afterColor}}">
		<block wx:for="{{carouselUrls}}" wx:key="index">
			<swiper-item class="carousel-image">
				<image src="{{item}}" class="carousel-image" mode='scaleToFill' bindtap="uploadImage" />
			</swiper-item>
		</block>
	</swiper>
	<view class="commdity-name-type-view">
		<view class="display_flex_between">
			<view class="flex_column commdity-name">
				<text class="name">{{data.name}}</text>
				<text decode="true" class="price">￥{{data.price}}</text>
			</view>
			
			<view class="collect-in">
				<button class="go-to-shop theme-bg" bindtap="businessTap" data-id="{{data.shopId}}" size="mini"
					style="font-size:24rpx;">进店</button>
				<view class="flex_column">
            <text class="iconfont iconshoucang1 {{isCollect?'is-collect':''}}"
            bindtap="{{isCollect?'bindDeleteCollect':'bindInsertCollect'}}" data-goodsid="{{data.id}}"></text>
            <text class="shoucang-text {{isCollect?'is-collect':'not-collect'}}">收藏</text>
         </view>
				
			</view>
		</view>

		<!-- <view class="commdity-engname">{{data.name}}</view>
		<view class="commdity-type-item" wx:for="{{specList}}" wx:key="index" wx:for-index='key'>
			<view class="commdity-type-name">{{key}}</view>
			<radio-group class="radio-group" bindchange="radioChange" data-firstIndex="{{key}}">
				<label wx:for="{{item}}" wx:key="index" class="group-label theme-color-border {{!item.stock?'disabled-group-label':''}} {{item.checked?'active theme-bg':''}} out_of_range one_row">
					<radio value="{{index}}" checked="{{item.checked}}" disabled="{{!item.stock}}" class="radio" /> {{item.name}}
				</label>
			</radio-group>
		</view> -->
	</view>
	<view class="commodity-detail-view">
		<view class="detail-title">
			<view>商品详情</view>
			<view wx:if="{{!shopInfo.isOutofDeliveryRange&&shopInfo.isOperatingOfShop&&shopInfo.shop.isOperating}}">
				<view class='stepper' wx:if="{{data.isShopCart}}">
					<block>
						<view class='flex_center car_reduce_add reduce-class' bindtap='bindMinus' data-cartId="{{data.cartId}}"
							data-number="{{data.number}}">－</view>
						<input disabled type='number' value='{{data.number}}' class='radd-reduce-input'></input>
						<view class="flex_center car_reduce_add add-class" data-goodsId="{{data.id}}"
							bindtap="{{data.goodsStatus!=4?'openSpecifications':''}}">＋</view>
					</block>
				</view>
				<block wx:if="{{!data.isShopCart}}">
					<view class="insert-view theme-bg" data-goodsId="{{data.id}}"
						bindtap="{{data.goodsStatus!=4?'openSpecifications':''}}">＋</view>
				</block>
			</view>
		</view>
		<view class="commodity-detail">
			{{data.detail?data.detail:"暂无详情"}}
		</view>
	</view>
	<!-- <navigator class="navigator-class" url="../../mine/share/index/index?inviterId={{userInfo.id}}">
		<view class="invite-wrapper">
			<image src="https://shop-beingwell.oss-cn-hangzhou.aliyuncs.com/data/images/business/share-invite/share_mine.png?v={{timestamp}}"
				mode="aspectFill" class="invite-image"></image>
		</view>
	</navigator> -->
</scroll-view>
<view class="shopping-cart-detail">
	<view class="content-fullReductionRuleName">
		{{!shopInfo.isOutofDeliveryRange?shopInfo.isOperatingOfShop&&shopInfo.shop.isOperating?fullPriceReductionIsHidden?fullReductionRuleName:'暂无优惠':'商家已休息，暂不接单':'超出配送范围'}}
	</view>
	<view
		class="shopping-cart-content {{shopInfo.isOutofDeliveryRange||!shopInfo.isOperatingOfShop||!shopInfo.shop.isOperating?'isOutofDeliveryRange':''}}">
		<view class="shopping-cart-left"
			bindtap="{{shopInfo.isOutofDeliveryRange||!shopInfo.isOperatingOfShop?'':'openShoppingCart'}}">
			<view>
				<view class="iconfont icongouwuche1 highlight {{shoppingCartList.length<=0?'theme-other-bg':'theme-bg'}}">
					<view class="num" wx:if="{{totalNum > 0}}">{{totalNum}}</view>
				</view>
			</view>
			<view>
				<view class="shopping-cart-totalPrice">
					<view class="{{fullPriceReductionIsHidden?'fullPriceReductionClass':'totalPrice'}}"
						wx:if="{{shoppingCartList.length>0}}">￥{{totalPrice}}</view>
					<view class="full-price-reduction" wx:if="{{fullPriceReductionIsHidden&&shoppingCartList.length>0}}">
						￥{{fullPriceReduction}}</view>
					<view class="not-full-price-reduction" wx:if="{{shoppingCartList.length<=0}}">暂未选购商品</view>
				</view>
				<!-- <view class="shopping-cart-desc">另需配送费￥{{deliveryPrice}}元</view> -->
			</view>
		</view>
		<view class="{{isStartDeliveryPrice?'theme-bg':'shopping-cart-bg'}} shopping-cart-right"
			bindtap="{{shoppingCartList.length<=0||!isStartDeliveryPrice||shopInfo.isOutofDeliveryRange||!shopInfo.isOperatingOfShop?'':'goToPay'}}">
			{{isStartDeliveryPrice?"去结算":"差 ￥"+priceDifference+" 起送"}}
		</view>
	</view>
	<mp-halfScreenDialog show="{{shoppingCartDialog}}" extClass="extClassShoppingCart" bind:close="closeShoppingCart">
		<view slot="title">已选商品</view>
		<view slot="desc">
			<scroll-view style="height: 55vh;" scroll-y>
				<view class="shoppingCart-item" wx:for="{{shoppingCartList}}" wx:key="key">
					<view class="goodsName-restructure-view">
						<view class="goodsName out_of_range one_row">{{item.goodsName}}</view>
						<view class="restructure out_of_range one_row">{{item.restructure}}</view>
					</view>
					<view class="goodsPrice-number-view">
						<view class="goodsPrice">￥{{item.price}}</view>
						<view class='stepper'>
							<view class='flex_center car_reduce_add reduce-class' bindtap='bindMinus' data-cartId="{{item.id}}"
								data-number="{{item.number}}">－</view>
							<input disabled type='number' value='{{item.number}}' class='add-reduce-input'></input>
							<view bindtap='bindPlus' class="flex_center car_reduce_add add-class" data-num="{{index}},{{item.number}}">＋</view>
						</view>
					</view>
				</view>
				<view class="goodsName-packingCharges">
					<text class="goodsName">包装费</text>
					<text class="goodsPrice">￥{{packingCharges}}</text>
					<text></text>
				</view>
			</scroll-view>
		</view>
	</mp-halfScreenDialog>
	<mp-halfScreenDialog show="{{specificationsDialog}}" extClass="extClassSpecifications">
		<view slot="title">选择规格</view>
		<view slot="desc">
			<view class="goods-info-view">
				<image src="{{data.mainImage}}" mode="aspectFill" class="commodity-image"></image>
				<view>
					<view class="goods-info-name">{{data.name}}</view>
					<view class="goods-info-specListString">已选：{{specListString}}</view>
					<view class="goods-info-price">￥{{priceAfter}}</view>
				</view>
			</view>
			<scroll-view scroll-y style="height: 56vh;">
				<view class="commdity-name-type-view">
					<!-- <view class="commdity-name">{{data.name}}</view> -->
					<view class="commdity-type-item" wx:for="{{specList}}" wx:key="index" wx:for-index='key'>
						<view class="commdity-type-name">{{key}}</view>
						<radio-group class="radio-group" bindchange="radioChange" data-firstIndex="{{key}}">
							<label wx:for="{{item}}" wx:key="index"
								class="group-label theme-border {{!item.stock?'disabled-group-label':''}} {{item.checked?'active theme-bg':'theme-color-border'}} out_of_range one_row">
								<radio value="{{index}}" checked="{{item.checked}}" disabled="{{!item.stock}}" class="radio" />
								{{item.name}}
							</label>
						</radio-group>
					</view>
				</view>
			</scroll-view>
			<view slot="footer">
				<view class="good-choice-btn theme-bg" bindtap="insertShoppingCart">我选好了</view>
			</view>
		</view>
	</mp-halfScreenDialog>
</view>
<view class="balance-views theme-bg">
  <view class="balance-text">
    当前余额（元）
  </view>
  <view class="balance-view">
    {{userInfo.balance}}
  </view>
  <view class="balance-detail">
    <view class="balance-detail-info">
      <view class="balance-quota">￥{{userInfo.totalBalance}}</view>
      <view class="balance-title">累计余额</view>
    </view>
    <view class="balance-detail-info">
      <view class="balance-quota">￥{{userInfo.totalConsumeBalance}}</view>
      <view class="balance-title">累计消费</view>
    </view>
    <view class="balance-detail-info">
      <view class="balance-quota">￥{{userInfo.totayGainBalance}}</view>
      <view class="balance-title">今日获得</view>
    </view>
  </view>
</view>
<swiper current="{{currentTab}}" class="swiper-box" duration="300" style="height:{{winHeight-10}}px"
  bindchange="bindSlideChange">
  <swiper-item class="swiper-items">
    <scroll-view bindscrolltoupper="onPullDownRefresh" upper-threshold="-50" bindscrolltolower="onReachBottom"
      lower-threshold="10" scroll-y class='scroll-views'>
      <view class='integral-items' wx:for="{{list}}" wx:key="index" bindtap="openSpecifications" data-id="{{item.id}}">
        <view class="integral-item">
          <view class="integral-item-name">
            <text>{{item.message}}</text>
          </view>
          <view class="integral-item-time">
            <text>{{item.createTime}}</text>
          </view>
        </view>
        <view class="integral-item-number {{item.operateType==1?'theme-color':'color-integral'}}">
          {{item.operateType==1?"+":"-"}}{{item.number}}
        </view>
      </view>
      <is-show-tip isShow="{{list.length<=0}}" top="25"
        bottom="0" text="{{list.length<=0}}" tipText="暂无记录" button="{{button}}"></is-show-tip>
    </scroll-view>
  </swiper-item>
</swiper>
<mp-halfScreenDialog show="{{specificationsDialog}}" extClass="extClassSpecifications">
  <view slot="title">账单详情</view>
  <view slot="desc">
    <view class="order-info-view">
        <view class="order-title">账单说明</view>
       <view class="order-info">{{recordInfo.message}}</view>
       <view class="order-title">账单金额</view>
       <view class="order-info">{{recordInfo.operateType==1?"+":"-"}}{{recordInfo.number}}元</view>
       <view class="order-title">支付时间</view>
       <view class="order-info">{{recordInfo.createTime}}</view>
       <view class="order-title" wx:if="{{recordInfo.serviceFee}}">服务费</view>
       <view class="order-info" wx:if="{{recordInfo.serviceFee}}">{{recordInfo.serviceFee}}元</view>
    </view>
  </view>
</mp-halfScreenDialog>
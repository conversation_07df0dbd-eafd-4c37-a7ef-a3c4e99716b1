page{
  padding-bottom: 120rpx;
  background: #f5f5f5;
}

.searchbar-result {
   margin-top: 0;
   font-size: 14px;
}

.searchbar-result:before {
   display: none;
}
.swiper-content {
   margin: 0 20rpx 20rpx 20rpx;
   border-radius: 15rpx;
   background-color: white;
   display: flex;
   align-items: center;
   justify-content: space-between;
}

.swiper-tab {
   width: 40%;
   text-align: center;
   height: 88rpx;
   line-height: 88rpx;
   display: flex;
   flex-flow: row;
   justify-content: space-between;
   background: #fff;
   z-index: 1;
   border-radius: 15rpx 15rpx 0 0;
   /* border-bottom: 1rpx solid #ededed; */
}

.collect-tab{
   width: 100%;
}

.swiper-tab-item {
   width: 50%;
}

.swiper-box {
   /* height: 400rpx; */
   border-radius: 0 0 15rpx 15rpx;
}

.manager-checkbox{
  margin-right: 20rpx;
  font-size: 28rpx;
}

.show-checkbox {
   position: fixed;
   bottom: 0;
   z-index: 9999;
   display: flex;
   align-items: center;
   justify-content: space-between;
   padding: 20rpx;
   font-size: 35rpx;
   width: 95%;
   background-color: white;
}

.checkbox-class{
   width: 60%;
}

.all-checkbox{
   font-size: 30rpx;
   width: 60%;
}

.iconshanchuguan{
   font-size: 40rpx;
   color: red;
}

.weui-slidecell {
   display: flex;
   justify-content: space-between;
   background-color: #fff;
   border-radius: 8px;
   padding: 20rpx;
   margin:10rpx 20rpx;
   line-height: 1.4;
   font-size: 17px;
   color: rgba(0, 0, 0, 0.9);
 }
 
 .is-first {
   margin-top: 20rpx;
 }
 
 .commdity-item {
   display: flex;
   justify-content: flex-start;
   width: 100%;
   height: 188rpx;
 }
 
 .commodity-icon {
   width: 38%;
   height: 100%;
   margin-right: 20rpx;
   border-radius: 10rpx;
 }
 
 .commdity-types {
   flex-direction: column;
   justify-content: space-between;
   width: 60%;
 }
 
 .commodity-name-type {
   margin-bottom: 5rpx;
 }
 
 .commodity-image {
   width: 190rpx;
   height: auto;
   border-radius: 5rpx;
 }
 
 .weui-slideview_icon .weui-slideview__btn__wrp:first-child {
   display: flex;
   align-items: center;
 }
 
 .weui-slideview_icon .weui-slideview__btn {
   background-color: red;
 }
 
 .checkbox-group-label {
   display: flex;
   align-items: center;
 }
 
 .commdity-name {
   font-size: 26rpx;
   font-weight: bold;
 }
 
 .types {
   font-size: 22rpx;
   color: #858585;
 }
 
 /*checkbox 选项框大小  */
 
 checkbox .wx-checkbox-input {
   width: 40rpx;
   height: 40rpx;
 }
 
 /*checkbox选中后样式  */
 
 checkbox .wx-checkbox-input.wx-checkbox-input-checked {
   width: 40rpx;
   height: 40rpx;
 }
 
 checkbox {
   width: 50rpx;
   height: 50rpx;
   margin-right: 15rpx;
 }
 
 .commdity-money-add-subtract {
   width: 50%;
   display: flex;
   flex-direction: column;
 }
 
 .commdity-money {
   font-size: 26rpx;
   font-weight: bold;
   display: flex;
   align-items: center;
 }

 .is-goods-exists{
    background-color: #d1d1d1;
    color: white;
    font-size: 24rpx;
    border-radius: 20rpx;
    padding:2rpx 10rpx
 }
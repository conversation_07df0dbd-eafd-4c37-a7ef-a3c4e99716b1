<view class="top-carousel-swiper" wx:if="{{carouselUrls.length>0}}">
   <swiper indicator-dots="{{indicatorDots}}" class="carousel-swiper" autoplay="{{autoplay}}" interval="{{interval}}"
      duration="{{duration}}" indicator-active-color="{{afterColor}}">
      <block wx:for="{{carouselUrls}}" wx:key="index">
         <swiper-item class="carousel-swiper-item">
            <image src="{{item.imagePath}}" class="carousel-image" mode='aspectFill'
               data-imageLinkUrl="{{item.imageLinkUrl}}" bindtap="carouseCommodityDetailTap" />
         </swiper-item>
      </block>
   </swiper>
</view>
<view class="menu-items">
   <view class="menu-item" wx:for="{{classifyList}}" wx:key="index"
      bindtap="bindToMenu" data-shopid="{{item.shipId}}" hover-class="hover-class-public">
      <image mode="aspectFit" class="menu-image" src="{{item.iconUrl}}" wx:if="{{item.icon}}"></image>
      <view class="menu-title out_of_range one_row">{{item.name}}</view>
   </view>
</view>

<view class="like-items">
   <view class="like-item" wx:for="{{recommendGoodsList}}" wx:key="index" hover-class="hover-class-public"
      bindtap="commodityDetailTap" data-shopid="{{item.shopId}}" data-id="{{item.id}}">
      <image src="{{item.mainImage}}" mode="scaleToFill" class="icon-like-class"></image>
      <view class="fullname-class">
         <text class="name-text out_of_range two_row">{{item.name}}</text>
      </view>
      <view class="like-money-view">
         <view class="like-money">
            <text>￥</text>
            <text>{{item.price}}<text class="score-money" decode="true">&nbsp;&nbsp;(可用积分等额兑换)</text></text>
         </view>
      </view>
      <view class="like-money-view">
         <text class="latelyMonthlySales out_of_range one_row">已售：{{item.latelyMonthlySales}}</text>
         <view class="insert-car" catchtap="openSpecifications" data-index="{{index}}">
            <text class="iconfont icongouwuche1 theme-bg"></text>
         </view>
      </view>
   </view>
   
</view>
<view wx:if="{{isMore}}" class="list-ismore">—————— 没有更多啦 ——————</view>
<mp-halfScreenDialog show="{{specificationsDialog}}" extClass="extClassSpecifications">
   <view slot="title">选择规格</view>
   <view slot="desc">
      <view class="goods-info-view">
         <image src="{{goods.mainImage}}" mode="aspectFill" class="commodity-image"></image>
         <view>
            <view class="goods-info-name out_of_range one_row">{{goods.name}}</view>
            <view class="goods-info-specListString">已选：{{specListString}}</view>
            <view class="goods-info-price">￥{{priceAfter}}</view>
         </view>
      </view>
      <scroll-view scroll-y style="height: 52vh;">
         <view class="commdity-name-type-view">
            <!-- <view class="commdity-name">{{data.name}}</view> -->
            <view class="commdity-type-item" wx:for="{{specList}}" wx:key="index" wx:for-index='key' wx:if="{{specList}}">
               <view class="commdity-type-name">{{key}}</view>
               <radio-group class="radio-group" bindchange="radioChange" data-firstIndex="{{key}}">
                  <label wx:for="{{item}}" wx:key="index"
                     class="group-label theme-border {{!item.stock?'disabled-group-label':''}} {{item.checked?'active theme-bg':'theme-color-border'}} out_of_range one_row">
                     <radio value="{{index}}" checked="{{item.checked}}" disabled="{{!item.stock}}" class="radio" />
                     {{item.name}}
                  </label>
               </radio-group>
            </view>
            <is-show-tip isShow="{{!specList}}" top="20"
			bottom="20" text="{{!specLis}}" tipText="暂无规格"></is-show-tip>
         </view>
         
      </scroll-view>
      
      <view slot="footer">
         <view class="good-choice-btn theme-bg" bindtap="insertShoppingCart" data-goodsid="{{goods.id}}">我选好了</view>
      </view>
   </view>
</mp-halfScreenDialog>
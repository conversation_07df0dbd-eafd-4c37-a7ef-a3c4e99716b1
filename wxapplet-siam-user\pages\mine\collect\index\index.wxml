<view style="height:100%">
   <view class="position-sticky">
      <view class="swiper-content">
         <view class="swiper-tab collect-tab self-adaption">
            <view class="swiper-tab-item" wx:for="{{tabList}}" wx:key="index" data-current="{{index}}"
               bindtap="clickCollectTab" hover-class='hover-click-class'>
               <view class="swiper_table_item_view {{collectTab==item.modeId?'active':''}}" data-current="{{index}}"
                  bindtap="clickCollectTab">
                  {{item.modeName}}
               </view>
            </view>
         </view>
      </view>
      <view> 
         <mp-searchbar bindselectresult="selectResult" search="{{search}}" bindclear="searchInputClear"
            bindhide="searchInputClear" placeholder="商品名称"></mp-searchbar>
      </view>
      <view class="swiper-content">
         <view class="swiper-tab self-adaption">
            <view class="swiper-tab-item" wx:for="{{modeList}}" wx:key="index" data-current="{{index}}"
               bindtap="clickTab" hover-class='hover-click-class'>
               <view class="swiper_table_item_view {{currentTab==item.modeId?'active':''}}" data-current="{{index}}"
                  bindtap="clickTab">
                  {{item.modeName}}
               </view>
            </view>
         </view>
         <view class="manager-checkbox" wx:if="{{!isShowCheckbox}}" bindtap="bindShowCheckbox">管理</view>
         <view class="manager-checkbox theme-color" wx:else bindtap="bindShowCheckbox">完成</view>
      </view>
   </view>
   <checkbox-group class="weui-slidecells" bindchange="checkboxChange">
      <view class="page__bd">
         <view class="weui-slidecells" wx:for="{{collectList}}" wx:key="index">
            <mp-slideview buttons="{{slideButtons}}" icon="{{true}}" data-goodsid="{{item.goodsId}}"
               bindbuttontap="slideButtonTap" throttle="300">
               <view class="weui-slidecell {{item.disable?'isDisable':''}}">
                  <label class="checkbox-group-label" wx:if="{{isShowCheckbox}}">
                     <checkbox value="{{index}}" index='{{index}}' class="theme-color theme-border-color"
                        checked="{{item.checked}}" disabled="{{item.disable}}" />
                  </label>
                  <view class="commdity-item" data-id="{{item.goodsId}}" data-shopid="{{item.shopId}}"
                     catchtap="{{collectTab==0?'bindMemberDetails':'bindPointsMallDetails'}}">
                     <image src="{{item.mainImage?item.mainImage:'../../assets/images/load-image.png'}}"
                        mode="aspectFill" class="commodity-icon"></image>
                     <view class="sell-out out-store" wx:if="{{item.goodsStatus==1}}">未上架</view>
                     <view class="sell-out out-store" wx:elif="{{item.goodsStatus==3}}">已下架</view>
                     <view class="sell-out out-store" wx:elif="{{item.goodsStatus==4}}">已售罄</view>
                     <view class="commdity-types">
                        <view class="commodity-name-type">
                           <view class="commdity-name">{{item.goodsName}}</view>
                           <view class="types">{{item.restructure}}</view>
                        </view>
                        <view class="commdity-money strike_through" wx:if="{{isSale}}">
                           ￥{{item.goodsPrice}}</view>
                        <view class="commdity-money">
                           ￥{{item.isSale?item.salePrice:item.goodsPrice}}</view>
                        <text class="is-goods-exists" wx:if="{{!item.isGoodsExists}}">已失效</text>
                     </view>
                  </view>
               </view>
            </mp-slideview>
         </view>
      </view>
   </checkbox-group>
</view>
<view wx:if="{{isShowCheckbox}}" class="show-checkbox">
   <view class="checkbox-class">
      <checkbox class="all-checkbox" bindtap="allChange" checked="{{isAlls}}">全选</checkbox>
   </view>
   <text class="iconfont iconshanchuguan" bindtap="batchDelete"></text>
</view>
<is-show-tip isShow="{{collectList.length<=0}}" src="{{noDataTip}}" top="25" bottom="20"
   text="{{collectList.length<=0}}" tipText="暂无收藏"></is-show-tip>
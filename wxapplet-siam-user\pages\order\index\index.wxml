<view class="">
	<view class="swiper-content position-sticky">
		<view class="swiper-tab self-adaption">
			<view class="swiper-tab-item" wx:for="{{tabList}}" wx:key="index" data-current="{{index}}" bindtap="clickTab"
				hover-class='hover-click-class'>
				<view class="swiper_table_item_view {{currentTab==item.modeId?'active':''}}" data-current="{{index}}"
					bindtap="clickTab">
					{{item.modeName}}
				</view>
			</view>
		</view>
	</view>
	<view>
		<mp-searchbar bindselectresult="selectResult" search="{{search}}" bindclear="searchInputClear"
			bindhide="searchInputClear" placeholder="请输入联系人手机号/订单号"></mp-searchbar>
	</view>
	<scroll-view class="swiper-content" wx:if="{{currentTab==0}}" scroll-x="true">
		<view class="swiper-tab self-adaption">
			<view class="swiper-tab-item two-tab" wx:for="{{shopOrderTabList}}" wx:key="index" data-current="{{index}}"
				bindtap="clickOrderTab" hover-class='hover-click-class'>
				<view class="swiper_table_item_view {{currentOrderTab==item.modeId?'active':''}}" data-current="{{index}}"
					bindtap="clickOrderTab" style="position: relative;margin-right: 10px;">
					<text class="out_of_range one_row">{{item.modeName}}</text>
					<!-- <text class="tab-number" wx:if="{{item.number>0}}">({{item.number}})</text> -->
					<mp-badge wx:if="{{item.number>0}}" content="{{item.number}}" style="position: absolute;top: -.9em;right: -.8em;" />
				</view>
			</view>
		</view>
	</scroll-view>
	<scroll-view class="swiper-content" wx:else scroll-x="true">
		<view class="swiper-tab self-adaption">
			<view class="swiper-tab-item two-tab" wx:for="{{pointsOrderTabList}}" wx:key="index" data-current="{{index}}"
				bindtap="clickOrderTab" hover-class='hover-click-class'>
				<view class="swiper_table_item_view {{currentOrderTab==item.modeId?'active':''}}" data-current="{{index}}"
					bindtap="clickOrderTab" style="position: relative;margin-right: 10px;">
					<text class="out_of_range one_row" decode="true">{{item.modeName}}</text>
					<!-- <text class="tab-number" wx:if="{{item.number>0}}">({{item.number}})</text> -->
					<mp-badge wx:if="{{item.number>0}}" content="{{item.number}}" style="position: absolute;top: -.9em;right: -.8em;" />
				</view>
			</view>
		</view>
	</scroll-view>
</view>

<swiper current="{{currentOrderTab}}" class="swiper-box" duration="300" bindchange="bindSlideChange"
	style="height:{{winHeight}}px;" wx:if="{{currentTab==0}}">
	<swiper-item class="swiper-items" wx:for="{{shopOrderTabList}}" wx:key="index">
		<scroll-view bindscrolltoupper="onPullDownRefresh" upper-threshold="-50" style="height:{{winHeight}}px;"
			bindscrolltolower="onReachBottom" lower-threshold="0" scroll-y class='scroll-views' wx:if="{{list.length>0}}">
			<view class="order-list-view" wx:for="{{list}}" wx:key="index">
				<view class="order-item flex_between" hover-class="hover-class-public" bindtap="orderDetailsTap" data-id="{{item.id}}" data-index="{{index}}" >
					<image src="{{item.shopLogoImg}}" class="commodity-image" mode="aspectFill"></image>
					<view class="right-item">
						<view class="address-status-info">
							<view class="address-view">
								<!-- <view class="order-mode theme-color-border">{{item.shoppingWay==1?"自取":"外卖"}}</view> -->
								<view class="address-info out_of_range one_row">
									{{item.shopName}}</view>
							</view>
							<view class="status-view">{{item.paymentModeText}}</view>
						</view>
						<view class="name-num-money">
							<view class="name-num flex_between">
								<text class="name-view out_of_range one_row" decode="true">{{item.description}}</text>
								<text class="num-view">x{{item. goodsTotalQuantity}}</text>
							</view>
							<view class="money-view">
								￥{{item.actualPrice}}
							</view>
						</view>
						<view class="tiem-view">
							{{item.createTime}}
							<text class="status-text theme-color">{{item.statusText}}</text>
						</view>
						
					</view>
				
				</view>
				<view class="tiem-view" wx:if="{{item.isAllowAppraise}}">
					<text></text>
					<button class="evaluate-btn theme-color theme-border" size="mini" data-id="{{item.id}}"
						data-shopId="{{item.shopId}}" catchtap="evaluateTip" style="font-size: 22rpx;margin-top:0;">评价</button>
				</view>
			</view>

		</scroll-view>
		<is-show-tip isShow="{{list.length<=0}}" bind:bindTap="goToDrink" buttonText="去喝一杯" top="45" bottom="10"
			text="{{list.length<=0}}" tipText="暂无{{shopOrderTabList[currentOrderTab].modeName}}订单"></is-show-tip>
	</swiper-item>
</swiper>
<swiper current="{{currentOrderTab}}" class="swiper-box" duration="300" bindchange="bindSlideChange"
	style="height:{{winHeight}}px;" wx:else>
	<swiper-item class="swiper-items" wx:for="{{pointsOrderTabList}}" wx:key="index">
		<scroll-view bindscrolltoupper="onPullDownRefresh" upper-threshold="-50" style="height:{{winHeight}}px;"
			bindscrolltolower="onReachBottom" lower-threshold="0" scroll-y class='scroll-views' wx:if="{{mallList.length>0}}">
			<view class="order-list-view" wx:for="{{mallList}}" wx:key="index">
				<view class="order-item flex_between" hover-class="hover-class-public" bindtap="orderMallDetailsTap" data-id="{{item.id}}" data-index="{{index}}" >
					<image src="{{item.firstGoodsMainImage}}" 
						class="commodity-image" mode="aspectFill"></image>
					<view class="right-item">
						<view class="address-status-info">
							<view class="address-view">
								<!-- <view class="order-mode theme-color-border">{{item.shoppingWay==1?"自取":"外卖"}}</view> -->
								<view class="address-info out_of_range one_row">
									{{item.contactCity}}{{item.contactArea}}{{item.contactStreet}}</view>
								<view class="address-info out_of_range one_row">
									{{item.shopName}}</view>
							</view>
							<view class="status-view">{{item.paymentModeText}}</view>
						</view>
						<view class="name-num-money">
							<view class="name-num flex_between">
								<text class="name-view out_of_range one_row" decode="true">{{item.description}}</text>
								<text class="num-view">x{{item. goodsTotalQuantity}}</text>
							</view>
							<view class="money-view">
								￥{{item.actualPrice}}
							</view>
						</view>
						<view class="tiem-view">
							{{item.createTime}}
							<text class="status-text theme-color">{{item.statusText}}</text>
						</view>
					</view>
				</view>
				<view class="tiem-view" wx:if="{{item.isAllowAppraise}}">
						<text></text>
						<button class="evaluate-btn theme-color theme-border" size="mini" data-id="{{item.id}}"
							data-shopId="{{item.shopId}}" catchtap="evaluateTip" style="font-size: 22rpx;margin-top:0;">评价</button>
					</view>
			</view>
		</scroll-view>
		<is-show-tip isShow="{{mallList.length<=0}}" bind:bindTap="goToDrink" buttonText="去喝一杯" top="45" bottom="10"
			text="{{mallList.length<=0}}" tipText="暂无{{pointsOrderTabList[currentOrderTab].modeName}}订单"></is-show-tip>
	</swiper-item>
</swiper>
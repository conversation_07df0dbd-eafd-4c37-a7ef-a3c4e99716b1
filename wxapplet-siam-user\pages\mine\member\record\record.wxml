<view>
  <view wx:for="{{list}}" wx:key="index" class="lists-class" bindtap="bindDetailInfo" data-id="{{item.id}}">
    <!-- <view class="orderNo">订单号：{{item.orderNo}}</view> -->
    <view class="list-top-class">
      <text>{{item.denominationName}}</text>
      <view>
        <text class="strike_through" wx:if="{{item.denominationIsSale}}">￥{{item.denominationSalePrice}}</text>
        <text decode="true" class="denominationSalePrice">&nbsp;&nbsp;￥{{item.denominationPrice}}</text>
      </view>
    </view>
    <!-- <view class="list-top-class createTime">
      <text>会员充值</text>
      <text>{{item.statusText}}</text>
    </view> -->
    <view class="list-top-class">
      <text class="createTime">{{item.createTime}}</text>
    </view>
    <!-- <view>
      <view wx:if="{{item.denominationIsGiveBalance}}">赠送余额：￥{{item.denominationGiveBalance}}</view>
      <view wx:if="{{item.denominationIsGiveCoupons}}">{{item.denominationGiveCouponsDescription}}</view>
    </view> -->
  </view>
  <is-show-tip isShow="{{list.length<=0}}" bind:bindTap="goToDrink" buttonText="去兑换" top="25" bottom="0" text="{{list.length<=0}}" tipText="暂无充值记录" button="{{button}}"></is-show-tip>
</view>

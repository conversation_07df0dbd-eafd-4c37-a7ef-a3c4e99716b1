page{
  background: #f5f5f5;
}

.top-class{
  padding: 20rpx;
  display: flex;
  align-items: center;
}

.shopLogoImg-class{
  width: 100rpx;
  height: 100rpx;
}

.shopInfo-name{
  margin-left: 20rpx;
  font-size: 28rpx;
}

.business-evaluate-view{
  margin: 0 20rpx 20rpx 20rpx;
  background: white;
  text-align: center;
  padding: 20rpx;
  border-radius: 20rpx;
}

.business-evaluate-title{
  font-size: 32rpx;
  font-weight: bold;
}

.textarea-view{
  margin-top: 40rpx;
}

textarea{
  padding: 20rpx;
  text-align: left;
  width: 94%;
  background: #f5f5f5;
  margin-bottom: 20rpx;
}

.submit-evaluate{
  width: 100%;
  padding: 20rpx 0;
  text-align: center;
  position: absolute;
  bottom: 0;
}

.uploader-imgs-item{
  width: 100rpx;
  height: 100rpx;
  margin-right: 20rpx;
}

.uploader-close-img{
  width: 30rpx;
  height: 30rpx;
  position: absolute;
  margin-left: 7%;
  margin-top: -10%;
}

.uploader-class-img{
  width: 100rpx;
  height: 100rpx;
}

.uploader-class{
  background: white;
  padding: 20rpx;
  margin: 0 20rpx;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
}

.textarea-tip{
  font-size: 32rpx;
  font-weight: bold;
}

.uploaderImages{
  display: flex;
  align-items: center;
}

.uploaderItems{
  display: flex;
  align-items: center;
  justify-content: center;
}

.images-num{
  text-align: end;
}

.iconshangchuantupian1{
  font-size: 100rpx;
  color: #a2a2a2;
  margin-left: 20rpx;
}
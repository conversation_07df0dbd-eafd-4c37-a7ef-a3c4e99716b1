.userinfo-item{
  line-height: 68rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  border-bottom: 0.5rpx solid #f5f5f5;
}

.userinfo-avatar {
  width: 108rpx;
  height: 108rpx;
  border-radius: 50%;
  box-shadow: -2px 0px 5px 0.5px rgba(0, 0, 0, 0),0px -2px 5px 1px rgba(0, 0, 0, 0.1),2px 0px 5px 1px rgba(0, 0, 0, 0),0px 2px 5px 1px rgba(0, 0, 0, 0.1)
}

.title-text{
  font-size: 28rpx;
  color: #8e8e8e;
}

.userinfo-text{
  font-size: 28rpx;
  text-align: end;
  font-weight: bold;
}

.input-email{
  width: 100%;
  text-align: center;
  border:1rpx solid #8e8e8e;
  padding: 10rpx 0;
  border-radius: 15rpx;
  margin: 20rpx 0;
  color: black;
}

.input-image{
  display: flex;
  align-items: center;
  width: 80%;
  justify-content: flex-end;
}

.out-login-btn{
  margin: 50rpx;
  text-align: center;
  line-height: 88rpx;
  color: white;
  border-radius: 10rpx;
  font-size: 30rpx;
}

.bind-wx{
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.radio-group-label{
  /* width: 100%; */
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 20rpx;
  font-weight: bold;
}

.placeholder-email{
  text-align: end;
}

.placeholder-text{
  text-align: end;
  color: #808090;
  font-size: 30rpx;
}

.title-tip{
  font-size: 22rpx;
  color: red;
}

/* .sex-radio-group{
  padding: 0 20rpx;
} */
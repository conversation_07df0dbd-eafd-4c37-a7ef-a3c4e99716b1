<view class="swiper-content position-sticky" wx:if="{{!prevData}}"> 
    <view class="swiper-tab self-adaption"> 
        <view class="swiper-tab-item" wx:for="{{tabList}}" wx:key="index" data-current="{{index}}" bindtap="clickTab" 
            hover-class='hover-click-class'> 
            <view class="swiper_table_item_view {{currentTab==item.modeId?'active':''}}" data-current="{{index}}" 
                bindtap="clickTab"> 
                {{item.modeName}} 
            </view> 
        </view> 
    </view> 
</view> 
<swiper current="{{currentTab}}" class="swiper-box" duration="300" bindchange="bindSlideChange" 
    style="height:{{winHeight}}px;"> 
    <swiper-item class="swiper-items"> 
        <scroll-view bindscrolltoupper="onPullDownRefresh" upper-threshold="-50" style="height:{{winHeight}}px;" 
            bindscrolltolower="onReachBottom" lower-threshold="0" scroll-y class='scroll-views' wx:if="{{couponsList.length>0}}"> 
            <view class="coupins-items"> 
                <radio-group> 
                    <view class="coupins-item {{item.couponsMemberRelationMap.isRelation?'':'isRelation'}}" 
                        wx:for="{{couponsList}}" wx:key="index"> 
                        <view class="item-top"> 
                            <view> 
                                <view class="couponsName"> 
                                    {{item.couponsMemberRelationMap.couponsName}}-{{item.couponsMemberRelationMap.preferentialTypeText}} 
                                </view> 
                                <view class="endTime">有效期至{{item.couponsMemberRelationMap.endTime}}</view> 
                            </view> 
                            <view class="theme-color"> 
                                <view class="couponsNum"> 
                                    {{item.couponsMemberRelationMap.preferentialType==1?(item.couponsMemberRelationMap.discountAmount*10)+"折":"满"+item.couponsMemberRelationMap.limitedPrice+"减"+item.couponsMemberRelationMap.reducedPrice}} 
                                </view> 
                                <view 
                                    wx:if="{{item.couponsMemberRelationMap.afterDiscountPrice>=0&&item.couponsMemberRelationMap.isRelation}}" 
                                    class="afterDiscountPrice"> 
                                    优惠{{item.couponsMemberRelationMap.afterDiscountPrice}}元 
                                </view> 
                            </view> 
 
                        </view> 
                        <view class="view-line"></view> 
                        <view class="bottom-view"> 
                            <view class="usage-rule" bindtap="onClick" data-index="{{index}}"> 
                                使用规则 
                                <text class="iconfont iconweibiaoti35" wx:if="{{item.couponsMemberRelationMap.isShow}}"></text> 
                                <text class="iconfont iconhtbArrowright02" wx:if="{{!item.couponsMemberRelationMap.isShow}}"></text> 
                            </view> 
                            <view 
                                class="immediate-use {{!item.couponsMemberRelationMap.isExpired&&!item.couponsMemberRelationMap.isUsed&&item.couponsMemberRelationMap.isRelation?'theme-color-border':'out-of-commission'}} " 
                                bindtap="{{!item.couponsMemberRelationMap.isExpired&&!item.couponsMemberRelationMap.isUsed&&item.couponsMemberRelationMap.isRelation?'onImmediateUse':''}}" 
                                wx:if="{{!item.couponsMemberRelationMap.checked}}" data-index="{{index}}">立即使用</view> 
                            <label class="checkbox-group-label" wx:else> 
                                <checkbox value="{{index}}" data-index='{{index}}' bindtap="onRadioChange" 
                                    class="theme-color theme-border-color" checked="{{item.couponsMemberRelationMap.checked}}" /> 
                            </label> 
                        </view> 
                        <view hidden="{{!item.couponsMemberRelationMap.isShow}}" class="usage-rule"> 
                            {{item.couponsMemberRelationMap.description}} 
                        </view> 
                    </view> 
                </radio-group> 
            </view> 
        </scroll-view> 
        <is-show-tip isShow="{{couponsList.length<=0}}" top="45" bottom="10" 
                    text="{{couponsList.length<=0}}" tipText="暂无{{tabList[0].modeName}}"></is-show-tip> 
    </swiper-item> 
    <swiper-item class="swiper-items"> 
        <scroll-view bindscrolltoupper="onPullDownRefresh" upper-threshold="-50" style="height:{{winHeight}}px;" 
            bindscrolltolower="onReachBottom" lower-threshold="0" scroll-y class='scroll-views' wx:if="{{couponsPointsList.length>0}}"> 
            <view class="coupins-items"> 
                <radio-group> 
                    <view class="coupins-item {{item.couponsMemberRelationMap.isRelation?'':'isRelation'}}" 
                        wx:for="{{couponsPointsList}}" wx:key="index"> 
                        <view class="item-top"> 
                            <view> 
                                <view class="couponsName"> 
                                    {{item.couponsMemberRelationMap.couponsName}}-{{item.couponsMemberRelationMap.preferentialTypeText}} 
                                </view> 
                                <view class="endTime">有效期至{{item.couponsMemberRelationMap.endTime}}</view> 
                            </view> 
                            <view class="theme-color"> 
                                <view class="couponsNum"> 
                                    {{item.couponsMemberRelationMap.preferentialType==1?(item.couponsMemberRelationMap.discountAmount*10)+"折":"满"+item.couponsMemberRelationMap.limitedPrice+"减"+item.couponsMemberRelationMap.reducedPrice}} 
                                </view> 
                                <view 
                                    wx:if="{{item.couponsMemberRelationMap.afterDiscountPrice>=0&&item.couponsMemberRelationMap.isRelation}}" 
                                    class="afterDiscountPrice"> 
                                    优惠{{item.couponsMemberRelationMap.afterDiscountPrice}}元 
                                </view> 
                            </view> 
 
                        </view> 
                        <view class="view-line"></view> 
                        <view class="bottom-view"> 
                            <view class="usage-rule" bindtap="onPointsClick" data-index="{{index}}"> 
                                使用规则 
                                <text class="iconfont iconweibiaoti35" wx:if="{{item.couponsMemberRelationMap.isShow}}"></text> 
                                <text class="iconfont iconhtbArrowright02" wx:if="{{!item.couponsMemberRelationMap.isShow}}"></text> 
 
                            </view> 
                            <view 
                                class="immediate-use {{!item.couponsMemberRelationMap.isExpired&&!item.couponsMemberRelationMap.isUsed&&item.couponsMemberRelationMap.isRelation?'theme-color-border':'out-of-commission'}} " 
                                bindtap="{{!item.couponsMemberRelationMap.isExpired&&!item.couponsMemberRelationMap.isUsed&&item.couponsMemberRelationMap.isRelation?'onImmediatePointsUse':''}}" 
                                wx:if="{{!item.couponsMemberRelationMap.checked}}" data-index="{{index}}">立即使用</view> 
                            <label class="checkbox-group-label" wx:else> 
                                <checkbox value="{{index}}" data-index='{{index}}' bindtap="onPointsRadioChange" 
                                    class="theme-color theme-border-color" checked="{{item.couponsMemberRelationMap.checked}}" /> 
                            </label> 
                        </view> 
                        <view hidden="{{!item.couponsMemberRelationMap.isShow}}" class="usage-rule"> 
                            {{item.couponsMemberRelationMap.description}} 
                        </view> 
                    </view> 
                </radio-group> 
            </view> 
        </scroll-view> 
        <is-show-tip isShow="{{couponsPointsList.length<=0}}" top="45" bottom="10" 
                    text="{{couponsPointsList.length<=0}}" tipText="暂无{{tabList[1].modeName}}"></is-show-tip> 
    </swiper-item> 
</swiper>
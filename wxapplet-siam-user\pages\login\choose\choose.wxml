<view>
	<view class="userinfo-view">
		<image class="userinfo-avatar" src="{{userInfo.avatarUrl?userInfo.avatarUrl:'../../../assets/images/user-head.png'}}" mode="cover"></image>
		<view class="userinfo-text">{{userInfo.username}}</view>
		<view class="userinfo-text">欢迎回来</view>
	</view>
	<view class="button-view">

	<button class="getphonenumber" type="primary" bindgetphonenumber='getPhoneNumber' open-type='getPhoneNumber' style="font-size: 30rpx;" disabled="{{!agreedToTerms}}">手机号码快捷登录</button>
	<!-- <button class="getphonenumber" type="primary" wx:else
	bindtap="getUserProfile">获取用户信息</button> -->
	<button class="verification-code theme-color-border" hover-class="hover-class-public" bindtap="verificationCodeTap" style="font-size: 30rpx;" disabled="{{!agreedToTerms}}">手机号验证码登录</button>
		<!-- <button class="getphonenumber" type="primary" wx:if="{{hasUserInfo}}" bindgetphonenumber='getPhoneNumber' open-type='getPhoneNumber'>微信一键登录</button> -->
		<!-- <button class="getphonenumber" type="primary" wx:else
    open-type='getUserInfo' bindgetuserinfo='onLoad'>微信一键登录</button>
		<button class="verification-code theme-color-border" hover-class="hover-class-public" bindtap="verificationCodeTap">手机号验证码登录</button> -->
	</view>

	<!-- 用户协议和隐私政策 -->
	<view class="agreement-section">
		<view class="agreement-checkbox">
			<checkbox-group bindchange="onAgreementChange">
				<label class="checkbox-label">
					<checkbox value="agree" checked="{{agreedToTerms}}" color="#004ca0"/>
					<text class="agreement-text">我已阅读并同意</text>
					<text class="agreement-link" bindtap="navigateToUserAgreement">《用户服务协议》</text>
					<text class="agreement-text">和</text>
					<text class="agreement-link" bindtap="navigateToPrivacyPolicy">《隐私政策》</text>
				</label>
			</checkbox-group>
		</view>
	</view>
</view>
<view class="search-input-views position-sticky theme-bg" bindtap="searchBusinessTap">
	<view class="search-input-view">
		<text class="iconfont iconsousuo-copy"></text>
		<!-- <image src="../../assets/common/search.png" mode="widthFix" class="search-image-class"></image> -->
		<input class="search-input" placeholder-class="placeholder-search-class" confirm-type="search"
			placeholder="搜索商家、商品名称" bindinput="searchInput" focus="true" value="{{searchValue}}"
			bindconfirm="searchTap"></input>
	</view>
</view>
<scroll-view class="scroll-views" style="height:{{bussinessHeight}}px" scroll-y scroll-top="scrollTop">
	<view wx:if="{{!searchInput}}">
		<!-- <view class="history-hot-search">
			<view class="history-hot-search-title">
				<view>搜索历史</view>
				<image src="../../assets/common/delete.png" mode="widthFix" class="delete-image-class"></image>
			</view>
			<view class="history-hot-items">
				<radio-group class="radio-group">
					<label class="group-label history-hot-item" bindtap="radioSearchTap" data-name="瑞幸">
						<radio class="radio"></radio>
						瑞幸
					</label>
				</radio-group>
			</view>
		</view> -->
		<!-- <view class="history-hot-search">
			<view class="history-hot-search-title">
				<view>热门搜索</view>
				<view></view>
			</view>
			<view class="history-hot-items">
				<radio-group class="radio-group">
					<label class="group-label history-hot-item">
						<radio class="radio"></radio>
						瑞幸
					</label>
				</radio-group>
			</view>
		</view> -->
	</view>

	<view wx:else>
		<view class="search-value" bindtap="searchTap" wx:if="{{searchTip}}">
			<text class="iconfont iconsousuo-copy"></text>
			<!-- <image src="../../assets/common/search.png" mode="widthFix" class="search-value-image-class"></image> -->
			<view class="search-value-class">查找“{{searchValue}}”</view>
		</view>
		<is-show-tip isShow="{{shopList.length<=0}}" top="25" bottom="20"
			text="{{shopList.length<=0}}" tipText="定位地址暂无商家"></is-show-tip>
		<view class="business-items margin-common" wx:if="{{shopList.length>0}}">
			<view class="business-item" wx:for="{{shopList}}" wx:key="key" wx:for-index="shopIndex">
				<view class="image-detail">
					<view class="main-image-num" style="width:25%">
						<image src='{{item.shopLogoImg}}' mode="aspectFill" class="business-image"></image>
						<view class="num" wx:if="{{item.shopCartNums>0}}">{{item.shopCartNums}}</view>
					</view>
					<view class="business-info">
						<view bindtap="businessTap" data-id="{{item.id}}">
							<view class="business-info-flex business-name">
								<text class="out_of_range one_row">{{item.name}}</text>
							</view>
							<view class="business-info-flex">
								<view class="business-sale">
									<text class="business-evaluate">★ {{item.serviceRating}}</text>
									<text class="business-right">月售：{{item.shopAdditionalVo.latelyMonthlySales}}</text>
								</view>
							</view>
							<view class="business-info-flex">
								<view class="business-sale">
									<text>起送￥{{item.startDeliveryPrice}}</text>
									<text decode="true">&nbsp;&nbsp;配送&nbsp;</text>
									<text wx:if="{{item.isfeeData}}">{{item.feeData}}</text>
									<text
										class="business-right {{item.isfeeData?'strike_through':''}}">￥{{item.shopAdditionalVo.deliveryFee}}</text>
								</view>
								<text>{{item.shopAdditionalVo.deliveryDurationText}}{{item.shopAdditionalVo.deliveryDistanceText}}</text>
							</view>
						</view>
						<view class="business-discount-info business-info-flex" bindtap="isPromotionTap"
							data-shopIndex="{{shopIndex}}">
							<view class="business-discount out_of_range one_row">
								<view class="theme-color-border business-discount-list"
									wx:for="{{item.shopAdditionalVo.promotionList}}" wx:key="index"
									wx:if="{{index<4&&item.shopAdditionalVo.promotionList.length>0}}" wx:for-item="rule">
									{{rule.name}}
								</view>
								<view wx:if="{{item.isfeeData}}" class="theme-color">
									配送费立减{{item.shopAdditionalVo.deliveryFee-item.feeData}}元
								</view>
							</view>
							<text class="iconfont iconweibiaoti35-copy"></text>
						</view>
					</view>
				</view>
				<view class="goods-list-view">
					<view class="goods-item" wx:for="{{item.goodsList}}" wx:key="index" wx:if="{{index<=2}}"
						bindtap="commodityDetailTap" data-shopid="{{item.shopId}}" data-id="{{item.id}}">
						<image src="{{item.mainImage}}" class="goods-image" mode="aspectFill"></image>
						<view class="goods-name out_of_range one_row">{{item.name}}</view>
						<view class="goods-price">￥{{item.price}}</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</scroll-view>
<mp-halfScreenDialog show="{{isActivityDialog}}" extClass="extClassSpecifications">
	<view slot="title">优惠活动</view>
	<view slot="desc">
		<scroll-view style="height: 30vh;" scroll-y>
			<view>
				<view wx:if="{{shopList[shopIndex].shopAdditionalVo.promotionList.length}}">
					<text class="dialog-title">优惠：</text>
					<view class="business-discount-info business-info-flex">
						<view class="business-discount out_of_range one_row">
							<view class="theme-color-border business-discount-list"
								wx:for="{{shopList[shopIndex].shopAdditionalVo.promotionList}}" wx:key="index"
								wx:for-item="rule">
								{{rule.name}}
							</view>
						</view>
					</view>
				</view>
				<view class="reduced-delivery-price" wx:if="{{shopList[shopIndex].reducedDeliveryPrice>0}}">
					<text class="dialog-title">配送费：</text>配送费立减{{shopList[shopIndex].reducedDeliveryPrice}}元
				</view>
			</view>
		</scroll-view>
	</view>
</mp-halfScreenDialog>
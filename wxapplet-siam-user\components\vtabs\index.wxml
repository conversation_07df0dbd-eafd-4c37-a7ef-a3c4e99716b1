<view class="weui-vtabs">
  <view class="weui-vtabs-bar__wrp {{tabBarClass}}">
    <scroll-view 
      scroll-y="{{ifScrolling}}"
      class="weui-vtabs-bar__scrollview"
      scroll-into-view="weui-vtabs-item__{{currentView}}"
      style="height:{{contentHeight}}px;"
    >
      <view class="weui-vtabs-bar__content">
        <block wx:for="{{vtabs}}" wx:key="title">
          <view 
            id="weui-vtabs-item__{{index}}"
            class="weui-vtabs-bar__item"
            data-index="{{index}}"
            bindtap="handleTabClick"
            style="position: relative;background-color: {{activeTab === index ?  tabBarActiveBgColor: tabBarInactiveBgColor}};color: {{activeTab === index ? tabBarActiveTextColor : tabBarInactiveTextColor}};">
            <view class="weui-vtabs-bar__title {{activeTab === index ? activeClass : ''}}">
              <text>{{item.name}}</text>
            </view>
            <view style="position: absolute;top:0;background-color: {{activeTab === index ?  tabBarActiveTextColor: tabBarInactiveBgColor}};color: {{activeTab === index ? tabBarActiveTextColor : tabBarInactiveTextColor}};width: 4px;height: 100%;"></view>
          </view>
        </block>
      </view>
    </scroll-view>
  </view>
  <view class="weui-vtabs-content__wrp">
    <scroll-view 
      scroll-y="{{ifScrolling}}"
      class="weui-vtabs-content__scrollview" 
      scroll-top="{{contentScrollTop}}"
      scroll-with-animation="{{animation}}"
      bindscroll="handleContentScroll"
      style="height:{{contentHeight}}px;"
    >
      <view class="weui-vtabs-content">
        <slot ></slot>
      </view>
    </scroll-view>
  </view>
</view>
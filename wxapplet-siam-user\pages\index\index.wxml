<view class="page">
  <view class='banner'>
    <view class="location-address" bindtap="shoppingAddressTap">
      <!-- <image src="../../assets/common/locate.png" mode="widthFix" class="place-image"></image> -->
      <view style="display: flex;">
        <text class="out_of_range one_row">{{addressInfo?addressInfo.city:'请授权位置定位'}}</text>
        <text class="iconfont iconweibiaoti35"></text>
      </view>
      <view class="search-input-views position-sticky" bindtap="searchBusinessTap" style="flex: 1;">
        <view class="search-input-view">
          <!-- <image src="../../assets/common/search.png" mode="widthFix" class="search-image-class"></image> -->
          <text class="iconfont iconsousuo-copy"></text>
          <text class="search-input"> 搜索商家、商品名称</text>
        </view>
      </view>
    </view>
  </view>
	<swiper indicator-dots="{{indicatorDots}}" class="carousel-swiper" autoplay="{{autoplay}}" interval="{{interval}}"
		duration="{{duration}}" indicator-active-color="{{afterColor}}" style="margin-top:10rpx;">
		<block wx:for="{{carouselUrls}}" wx:key="index">
			<swiper-item class="carousel-swiper-item">
				<image src="{{item.imagePath}}" class="carousel-image" mode='aspectFill'
					data-imageLinkUrl="{{item.imageLinkUrl}}" bindtap="carouseCommodityDetailTap" />
			</swiper-item>
		</block>
	</swiper>
  <swiper indicator-dots="{{false}}" class="mall-type-swiper" autoplay="{{false}}">
    <block wx:for="{{mallCategories}}" wx:key="index">
      <swiper-item class="mall-type-swiper-item">
        <block wx:for="{{item}}" wx:key="index" wx:for-item="c">
          <view class="mall-type-item"  bindtap="categoryTap"  data-id="{{c.id}}">
            <image src="{{c.categoryPicture}}" mode="aspectFill" class="mall-type-icon" />
            <text class="mall-type-name"  style="{{ checkedCategoryId != c.id ? '' : 'color: #ff6500' }}">{{c.categoryName}}</text>
          </view>
        </block>
      </swiper-item>
    </block>
  </swiper>
  
	<!-- <view wx:if="{{recommendGoodsList.length>0}}">
		<view class="recommend-business-title margin-common theme-color">商品推荐</view>
		<view class="like-items">
			<view class="like-item" wx:for="{{recommendGoodsList}}" wx:key="index" hover-class="hover-class-public"
				bindtap="commodityDetailTap" data-shopid="{{item.shopId}}" data-id="{{item.id}}">
				<view class="like-detail-view">
					<image src="{{item.mainImage}}" mode="aspectFill" class="icon-like-class"></image>
					<view class="fullname-class">
						<text class="name-text out_of_range one_row">{{item.name}}</text>
						<text class="name-text latelyMonthlySales out_of_range one_row">月售：{{item.latelyMonthlySales}}</text>
					</view>
					<view class="engname-class out_of_range one_row">{{item.detailmainImage}}</view>
				</view>
				<view class="like-money-view">
					<view class="like-money">
						<text>￥</text>
						<text>{{item.price}}</text>
					</view>
					<view class="theme-color new-commodity-button">立即购买>></view>
				</view>
			</view>
		</view>
	</view> -->
  <view class="main-content" id="main-mall">
    <!-- <view class="recommend-business-title margin-common theme-color">推荐商家</view> -->
    <view class="select-classfiy">
      <text class="select-classfiy-title" bindtap="classfiyTap" data-id="{{0}}">选择校区></text>
      <view>
        <text class="select-classfiy-item" style="{{ classfiyId == 1 ? 'color: #ff6500' : '' }}" bindtap="classfiyTap" data-id="{{1}}">北苑</text>
        <text class="select-classfiy-item" style="{{ classfiyId == 2 ? 'color: #ff6500' : '' }}" bindtap="classfiyTap" data-id="{{2}}">滨江</text>
        <text class="select-classfiy-item" style="{{ classfiyId == 3 ? 'color: #ff6500' : '' }}" bindtap="classfiyTap" data-id="{{3}}">新校区</text>
      </view>
    </view>
    <!-- <view class="mall-category">
      <scroll-view scroll-x="true" style="width: 100%" class="mall-category-scroll">
        <view class="mall-category-scroll-content">
          <block wx:for="{{mallCategoryList}}" wx:key="index">
            <view class="mall-type-item" bindtap="categoryTap"  data-id="{{item.id}}">
              <view class="mall-type-icon-content">
                <image src="{{item.categoryPicture}}" mode="aspectFill" class="mall-type-icon" />
              </view>
              <text class="mall-type-name" style="{{ checkedCategoryId != item.id ? '' : 'color: #ff6500' }}">{{item.categoryName}}</text>
            </view>
          </block>
        </view>
      </scroll-view>
    </view> -->
    <is-show-tip isShow="{{shopList.length<=0}}" top="25" button="{{shopList.length<=0}}" bottom="20" text="{{shopList.length<=0}}" bind:bindTap="getRegeo" tipText="定位地址暂无商家" buttonText="重新定位">
    </is-show-tip>
    <view class="business-items margin-common" wx:if="{{shopList.length>0}}">
      <view class="business-item" wx:for="{{shopList}}" wx:key="key" wx:for-index="shopIndex" hover-class="hover-class-public">
        <view class="main-image-num" style="width:25%">
          <image src='{{item.shopLogoImg}}' bindtap="businessTap" data-id="{{item.id}}" mode="aspectFill" class="business-image"></image>
          <!-- <view class="num" wx:if="{{shopCartNums[index].shopId==item.id&&shopCartNums[index].num>0}}">{{shopCartNums[index].num}}</view> -->
          <!-- <view class="num" wx:if="{{item.shopCartNums>0}}">{{item.shopCartNums}}</view> -->
        </view>
        <view class="business-info">
          <view bindtap="businessTap" data-id="{{item.id}}">
            <view class="business-name">
              <text class="out_of_range one_row">{{item.name}}</text>
              <view class="shop-status" wx:if="{{item.shopAdditionalVo.isOperatingOfShop}}">
                <text>· 营业中 ·</text>
              </view>
              <view class="shop-status shop-status-offline" wx:else>
                <text>· 打烊 ·</text>
              </view>
            </view>
            <view class="business-info-flex">
              <view class="business-sale">
                <text class="business-evaluate">★ {{item.serviceRating}}</text>
                <text class="business-right">月售：{{item.shopAdditionalVo.latelyMonthlySales}}</text>
              </view>
            </view>
            <view class="business-info-flex">
              <view class="business-sale">
                <text>起送￥{{item.startDeliveryPrice}}</text>
                <text wx:if="{{item.isfeeData}}" decode="true">&nbsp;&nbsp;免配送</text>
                <text wx:else decode="true">&nbsp;&nbsp;配送&nbsp;￥{{item.delivery_fee}}</text>
              </view>
              <text decode="true">{{item.shopAdditionalVo.deliveryDurationText}}&nbsp;&nbsp;{{item.deliveryRanges}}</text>
            </view>
          </view>
          <view class="coupons-info">
            <scroll-view scroll-x class="coupons-info-scroll">
              <view style="display: flex;flex: 1;justify-content: flex-start;align-items: center;">
                <view class="group-label group-label-{{rule.source}}" wx:for="{{item.shopAdditionalVo.couponsList}}" wx:if="{{item.shopAdditionalVo.couponsList.length>0}}" wx:key="index" wx:for-item="rule">
                  <text>{{rule.name}}</text>
                </view>
                <text class="group-label group-label-mall" wx:for="{{item.shopAdditionalVo.promotionList}}" wx:if="{{index<4&&item.shopAdditionalVo.promotionList.length>0}}" wx:key="index" wx:for-item="rule">{{rule.name}}</text>
                <image src='/assets/common/locate.png' mode="aspectFill" class="location-icon"></image>
                <text class="location-text">{{item.street}}</text>
              </view>
            </scroll-view>
          </view>
          <!-- <view class="business-discount-info business-info-flex" wx:if="{{item.shopAdditionalVo.promotionList.length>0||item.isfeeData}}" bindtap="isPromotionTap" data-shopIndex="{{shopIndex}}">
            <view class="business-discount out_of_range one_row">
              <label class="group-label" wx:for="{{item.shopAdditionalVo.promotionList}}" wx:if="{{index<4&&item.shopAdditionalVo.promotionList.length>0}}" wx:key="index" wx:for-item="rule">
                <radio class="radio"></radio>
                {{rule.name}}
              </label>
              <image src='/assets/common/locate.png' mode="aspectFill" class="location-icon"></image>
              <text class="location-text">{{item.street}}</text>
            </view>
            <text class="iconfont iconweibiaoti35-copy"></text>
          </view> -->
        </view>
      </view>
    </view>
  </view>
</view>
<mp-halfScreenDialog show="{{isActivityDialog}}" extClass="extClassSpecifications">
  <view slot="title">优惠活动</view>
  <view slot="desc">
    <scroll-view style="height: 30vh;" scroll-y>
      <view>
        <view wx:if="{{shopList[shopIndex].shopAdditionalVo.promotionList.length>0}}">
          <text class="dialog-title">优惠：</text>
          <view class="business-discount-info business-info-flex">
            <view class="business-discount out_of_range one_row">
              <view class="theme-color-border business-discount-list" wx:for="{{shopList[shopIndex].shopAdditionalVo.promotionList}}" wx:key="index" wx:for-item="rule">
                {{rule.name}}
              </view>
            </view>
          </view>
        </view>
        <view class="reduced-delivery-price" wx:if="{{shopList[shopIndex].reducedDeliveryPrice>0}}">
          <text class="dialog-title">配送费：</text>配送费立减{{shopList[shopIndex].reducedDeliveryPrice}}元
        </view>
      </view>
    </scroll-view>
  </view>
</mp-halfScreenDialog>
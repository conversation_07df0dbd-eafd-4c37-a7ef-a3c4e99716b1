<view class="weui-search-bar {{extClass}}">
    <view class="weui-search-bar__form">
        <view class="weui-search-bar__box">
            <icon class="weui-icon-search_in-box" type="search" size="12"></icon>
            <input type="text" class="weui-search-bar__input" placeholder="{{placeholder?placeholder:'搜索'}}" value="{{value}}" focus="{{focus}}" bindblur="inputBlur" bindfocus="inputFocus" bindinput="inputChange" />
            <view class="weui-icon-clear" wx:if="{{value.length > 0}}" bindtap="clearInput">
                <icon type="clear" size="12"></icon>
            </view>
        </view>
        <label class="weui-search-bar__label" hidden="{{searchState}}" bindtap="showInput">
            <icon class="weui-icon-search" type="search" size="12"></icon>
            <view class="weui-search-bar__text">{{placeholder?placeholder:'搜索'}}</view>
        </label>
    </view>
    <view wx:if="{{cancel && searchState}}" class="weui-search-bar__cancel-btn" bindtap="hideInput">{{cancelText}}</view>
</view>
<mp-cells class="searchbar-result  {{extClass}}" wx:if="{{searchState && result.length > 0}}">
    <mp-cell bindtap="selectResult" data-index="{{index}}" wx:for="{{result}}" hover>
        <view>{{item.text}}</view>
    </mp-cell>
</mp-cells>
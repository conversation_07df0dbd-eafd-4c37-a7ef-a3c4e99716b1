/*
 * Author: simsir-lin
 * Github: https://github.com/simsir-lin
 * Email: <EMAIL>
 */

.multiple-rate .star, .multiple-rate .star-o {
  width: 0;
  height: 0;
  color: red;
  position: relative;
  display: inline-block;
  vertical-align: middle;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 6px solid rgb(247, 186, 42);
  transform: rotate(180deg);
  /* margin: 0 5px; */
}

.multiple-rate .star::after, .multiple-rate .star::before,
.multiple-rate .star-o::after, .multiple-rate .star-o::before {
  content: '';
  width: 0;
  height: 0;
  color: red;
  position: absolute;
  display: inline-block;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 6px solid rgb(247, 186, 42);
  top: -0px;
  left: -8px;
}

.multiple-rate .star::after, .multiple-rate .star-o::after {
  content: '';
  transform: rotate(-68deg);
}

.multiple-rate .star::before, .multiple-rate .star-o::before {
  content: '';
  transform: rotate(68deg);
}

.multiple-rate .star-o {
  border-bottom: 7px solid #ccc;
}

.multiple-rate .star-o::after, .multiple-rate .star-o::before {
  border-bottom: 7px solid #ccc;
}

.heart,.heart-o {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: 8px;
  height: 8px;
  background-color: red;
  border-radius: 2px;
  transform: rotate(45deg);
  margin: 0 10px;
}

.heart:before,.heart:after,.heart-o:before,.heart-o:after {
  content: '';
  position: absolute;
  display: inline-block;
  vertical-align: middle;
  width: 8px;
  height: 8px;
  background-color: red;
  border-radius: 50%;
  top: 0px;
  left: -4px;
}

.heart:after, .heart-o:after {
  left: 0px;
  top: -4px;
}

.heart-o {
  background: #ccc;
}
.heart-o:before,.heart-o:after {
  background: #ccc;
}

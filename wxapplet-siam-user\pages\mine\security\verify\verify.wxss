page {
  background: #f5f5f5;
  width: 100%;
  margin: 0;
}

.items-class{
  padding: 20rpx;
  background: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 30rpx;
  font-weight: bold;
}

.text-class{
  font-size: 28rpx
}

.account-text{
  font-size: 30rpx;
  /* color: #6cc5ef; */
}

input{
  width: 100%;
}

.next-button{
  text-align: center;
  margin: 20rpx;
  margin-top: 100px;
  padding: 15rpx;
  border-radius: 15rpx;
  font-size: 30rpx;
}

.isSend{
  background-color: #cccccc;
  color: white;
}
page{
  background: #f5f5f5;
  width: 100%;
  margin: 0;
  padding-bottom: 0;
}

.swiper-content {
  border-radius: 15rpx;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  white-space: nowrap;
}

.swiper_table_item_view{
  margin-right: 10rpx;
}

.swiper-tab-item {
  width: 50%;
}

.two-tab{
  width: 20%;
}

.swiper-box {
  /* height: 400rpx; */
  border-radius: 0 0 15rpx 15rpx;
}

.swiper-tab{
  width: 100%;
  box-shadow: -2px 0px 5px 0.5px rgba(0, 0, 0, 0.1);
  text-align: center;
  height: 88rpx;
  line-height: 88rpx;
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  background: #fff;
  z-index: 1;
}

.swiper-tab-item{
  width: 50%;
}

.swiper-box{
  display: block; 
  width: 100%; 
  height: 100%; 
  overflow: hidden;
}

.swiper-items{
  height: 100%;
}

.scroll-views{
  height: 100%;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.order-list-view{
  margin: 0 20rpx;
  margin-bottom: 20rpx;
  background: white;
  border-radius: 10rpx;
}

.order-item{
  margin-top: 20rpx;
  padding: 20rpx;
  background: white;
  border-radius: 10rpx;
}

.address-view{
  display: flex;
  align-items: center;
  width: 70%;
}

.order-mode{
  width: 15%;
  text-align: center;
  height: 28rpx;
  font-size: 24rpx;
  padding: 2rpx;
  border-radius: 5rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rpx;
}

.address-info{
  font-size: 28rpx;
}

.address-status-info{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-view{
  font-size: 28rpx;
  color: #9f9f9f;
}

.name-num-money{
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 80rpx;
}

.name-num{
  width: 72%;
}

.name-view{
  font-size: 28rpx;
}

.num-view{
  margin-left: 40rpx;
  font-size: 30rpx;
}

.money-view{
  font-size:32rpx;
  font-weight: bold;
}

.tiem-view{
  font-size: 24rpx;
  line-height: 48rpx;
  color: #9f9f9f;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-text{
  font-weight: bold;
}

.no-data-view{
  height: 100%;
  text-align: center;
}

.no-data-icon{
  width: 30%;
  height: auto;
}

.no-data-tip{
  font-size: 28rpx;
  color: #434343;
}

.go-to-btn{
  font-size: 28rpx;
  margin: 50rpx 38%;
  padding: 10rpx;
  border-radius: 10rpx;
}

.evaluate-btn{
  margin: 20rpx;
}

.tab-number{
  border-radius: 50%;
}

.commodity-image{
  width: 160rpx;
  height: 160rpx;
  border-radius: 10rpx;
}

.right-item{
  width: 75%;
}
page {
  background-color: #f7f7f7;
  padding-top: 10rpx;
}

/* .weui-slidecells {
  margin-top: 10rpx;
} */

.weui-slidecell {
  display: flex;
  justify-content: space-between;
  margin: 10rpx 20rpx 10rpx 20rpx;
  background-color: #fff;
  border-radius: 8px;
  padding: 20rpx 15rpx;
  line-height: 1.4;
  font-size: 17px;
  color: rgba(0, 0, 0, 0.9);
}

.is-first {
  margin-top: 20rpx;
}

.commdity-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
}

.commodity-icon {
  width: 25%;
  height: 108rpx;
  margin-right: 20rpx;
  border-radius: 10rpx;
}

.commdity-types {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 72%;
}

.commodity-name-type {
  margin-bottom: 5rpx;
}

.commodity-image {
  width: 190rpx;
  height: auto;
  border-radius: 5rpx;
}

.weui-slideview_icon .weui-slideview__btn__wrp:first-child {
  display: flex;
  align-items: center;
}

.weui-slideview_icon .weui-slideview__btn {
  background-color: red;
}

.checkbox-group-label {
  display: flex;
  align-items: center;
  width: 75%;
}

.commdity-name {
  font-size: 26rpx;
  font-weight: bold;
}

.types {
  font-size: 22rpx;
  color: #858585;
}

/*checkbox 选项框大小  */

checkbox .wx-checkbox-input {
  width: 40rpx;
  height: 40rpx;
}

/*checkbox选中后样式  */

checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  width: 40rpx;
  height: 40rpx;
}

checkbox {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

.commdity-money-add-subtract {
  width: 50%;
  display: flex;
  flex-direction: column;
}

.commdity-money {
  font-size: 26rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
}

/*主容器*/

.stepper {
  margin-left: 20rpx;
}

/*加号和减号*/

.stepper text {
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  font-size: 28rpx;
}

/*数值*/

.stepper input {
  width: 30px;
}

/* 底部去支付样式 */

.settlement-view {
  position: fixed;
  bottom: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  z-index: 66;
  box-shadow: -2px 0px 5px 0.5px rgba(0, 0, 0, 0.1);
  background: white;
}

.total-payable-view {
  width: 65%;
  background: #fff;
  padding-left: 30rpx;
  display: flex;
  flex-direction: column;
}

.total-payable {
  display: flex;
  align-items: center;
  padding: 10rpx 0;
}

.baozhuangfei-class {
  font-size: 24rpx;
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.total-payable-money-view {
  margin-left: 20rpx;
}

.to-pay-view {
  /* width: 25%; */
  font-size: 28rpx;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10rpx;
}

.title-text {
  font-size: 28rpx;
  color: #858585;
}

.sign-icon {
  font-size: 32rpx;
  font-weight: bold;
}

.total-payable-money {
  font-size: 34rpx;
  font-weight: bold;
}

/* 猜你喜欢样式 */

.like-list-view {
  padding: 20rpx;
}

.title-like-view {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.title-view {
  font-size: 30rpx;
  font-weight: bold;
}

.icon-font-view {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}

.icon-change-class {
  width: 36rpx;
  height: auto;
  margin-right: 10rpx;
}

.icon-like-class {
  width: 100%;
  height: 100%;
  border-radius: 15rpx 15rpx 0 0;
}

.like-items {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 10px;
}

.like-item {
  height: 25vh;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background: white;
  border-radius: 15rpx;
  margin-bottom: 15rpx;
}

.item-two {
  margin: 0 3.5%;
}

.like-detail-view {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.fullname-class {
  margin-top: 11rpx;
  font-size: 26rpx;
  width: 90%;
}

.engname-class {
  font-size: 24rpx;
  color: #ccc;
  width: 90%;
}

.like-money-view {
  width: 90%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0 10rpx 0;
}

.like-money {
  font-size: 26rpx;
  font-weight: bold;
}

.plus-view {
  font-size: 28rpx;
  width: 38rpx;
  height: 38rpx;
  line-height: 38rpx;
  text-align: center;
  border-radius: 50%;
  color: white;
}

.out-store {
  left: 13.8%;
  font-size: 22rpx;
  height: 80rpx;
  line-height: 80rpx;
  width: 80rpx;
}

.full-reduction-view {
  font-size: 20rpx;
  font-weight: bold;
  padding: 0 10rpx;
  margin: 0 5rpx;
  border-radius: 10rpx;
}

.gotogg{
  text-align: center;
  margin: 0 35% 15% 35%;
  border-radius: 50rpx;
  padding: 10rpx 0;
  font-size: 30rpx;
}
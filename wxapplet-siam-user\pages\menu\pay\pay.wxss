page {
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

.top-tips-view {
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  line-height: 100rpx;
}

.probably-time-view {
  margin-left: 20rpx;
}

/* 单选框样式--自取配送 */

.radio-group-view {
  display: flex;
  align-items: center;
  width: 25%;
}

.radio-group {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  padding: 5rpx;
  border-radius: 50rpx;
  /* height: 66rpx; */
}

.radio-group-label {
  width: 46%;
  padding: 2%;
  font-size: 28rpx;
  border-radius: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  /* height: 60rpx; */
}

.not-active{
  color: white;
}

.ask-for-delivery-detail {
  margin: 20rpx;
  padding: 20rpx;
  background: white;
  border-radius: 15rpx;
}

.ask-for-delivery-title {
  font-size: 24rpx;
  font-weight: bold;
  line-height: 60rpx;
}

.ask-for-delivery {
  display: flex;
}

.ask-for-delivery-address {
  width: 75%;
}

.ask-for-delivery-house {
  width: 100%;
  font-size: 28rpx;
  display: flex;
  align-items: center;
}

.dizhi-phone-icon{
  width: 5%;
  height: auto;
}

.right-class{
  width: 90%;
}

.ask-for-delivery-address-detail {
  font-size: 28rpx;
  height: 40rpx;
  line-height: 40rpx;
  color: #7f7f7f;
  display: flex;
  align-items: center;
}

.ask-for-delivery-view{
  width: 90%;
}

.shopping-commodity-details {
  margin: 20rpx;
  padding: 20rpx;
  background: white;
  border-radius: 15rpx;
}

.commodity-name-price-detail {
  padding: 10rpx 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.commodity-name-types {
  width: 70%;
}

.commodity-name {
  font-size: 28rpx;
  font-weight: bold;
}

.commodity-types {
  font-size: 24rpx;
}

.commodity-totalnum {
  font-size: 30rpx;
}

.commodity-price {
  font-size: 28rpx;
  font-weight: bold;
}

.commodity-totalnum-price {
  width: 30%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.total-money-view {
  width: 100%;
  line-height: 40rpx;
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.total-title {
  font-size: 28rpx;
}

.total-money {
  font-size: 32rpx;
  font-weight: bold;
  margin-left: 20rpx;
  width: 40%;
}

.icon-wechat-pay {
  width: 50rpx;
  height: auto;
  margin-right: 20rpx;
}

.pay-mode-view {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  margin: 20rpx;
  padding: 20rpx;
  font-size: 30rpx;
  border-radius: 15rpx;
}

.choose-pay-mode {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
}
.coupon-mode{
  width: 80%;
}
.delivery-upstairs {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.delivery-upstairs .checkbox {
  border: 1rpx solid #004ca0;
  padding: 10rpx 30rpx;
  border-radius: 10rpx;
  font-size: 24rpx;
  margin-left: 20rpx;
}
.delivery-upstairs .checked {
  background-color: #004ca0;
  color: #fff;
}
.appointment-time-picker {
  font-size: 26rpx;
  margin-top: 30rpx;
}
.checked-time {
  border: 1px solid #004ca0;
  background-color: #004ba015;
  color: #004ca0;
  padding: 10rpx;
  border-radius: 10rpx;
}
.ask-for-remarks-view {
  background: white;
  margin: 20rpx;
  padding: 20rpx;
  border-radius: 15rpx;
}

.choose-ask-for {
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 100rpx;
  font-size: 30rpx;
}

.remarks-title-input-num {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.remarks-title {
  font-size: 30rpx;
  line-height: 100rpx;
}

.remarks-input-num {
  font-size: 28rpx;
}

/* .remarks-view {
  padding-bottom: 120rpx;
} */

.textarea-remarks {
  width: 94%;
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 10rpx;
  height: 120rpx;
  font-size: 28rpx;
  z-index: 0;
}

.iconwechat_pay{
  color: #09bb07;
  font-size:40rpx;
}

.iconyue{
  color: #f0dcab;
  font-size:40rpx;
}

.go-pay-view {
  position: fixed;
  bottom: 0;
  width: 100%;
  line-height: 100rpx;
  display: flex;
  align-items: center;
  background: white;
  box-shadow: -2px 0px 5px 0.5px rgba(0, 0, 0, 0.1);
  font-size: 36rpx;
  z-index: 99;
  border-top: 0.5rpx solid #f5f5f5;
}

.go-pay-money {
  padding: 0 20rpx;
  width: 70%;
  background: white;
  display: flex;
}

.more-pay {
  font-size: 32rpx;
}

.go-pay {
  width: 30%;
  color: white;
  text-align: center;
  font-size: 32rpx;
}

cover-view {
  line-height: 100rpx;
}

/*radio 选项框大小  */

radio .wx-radio-input {
  width: 35rpx;
  height: 35rpx;
  border-radius: 50%;
  border-color: #ededed;
}

.ask-for-label {
  display: flex;
  align-items: center;
}

.ask-for-radio-group {
  width: 60%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ra-group-label {
  display: flex;
  align-items: center;
  padding: 10rpx;
}

.full-reduction-view {
  font-size: 24rpx;
  font-weight: bold;
  padding: 0 10rpx;
  margin: 0 5rpx;
  border-radius: 10rpx;
}

.fullPriceReductionClass {
  color: gainsboro;
  text-decoration: line-through;
}

.pay-mode-title{
  width: 20%;
  font-size: 28rpx;
}

.after-discount{
  font-size: 28rpx;
  font-weight: bold;
  text-align: end;
  width: 100%;
  font-size: 28rpx;
}

.swiper-tab{
  width: 100%;
  text-align: center;
  height: 88rpx;
  line-height: 88rpx;
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  background: #fff;
  z-index: 1;
  border-bottom: 1rpx solid #ededed;
}

.swiper-tab-item{
  width: 50%; 
}

.swiper-box{
  display: block; 
  width: 100%; 
  height: 100%; 
  overflow: hidden;
}

.swiper-items{
  height: 100%;
}

.scroll-views{
  height: 100%;
  background: #fff;
}

.bindSlideChange{
  padding-top: 20rpx;
}

.space-between-class{
  width: 100%;
}

.bindSlideChange-class{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.currentTab1-view{
  margin-bottom: 20rpx;
}

.currentTab1-title{
  font-size: 26rpx;
  color: #7f7f7f;
}

.time-phone-view{
  border-top: 1rpx solid #f5f5f5;
  padding: 20rpx 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.time-phone-item{
  font-size: 24rpx;
}

.time-phone-content{
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.time-phone-title{
  
  color: #7f7f7f;
}

.deliveryAddress-info{
  margin-bottom: 20rpx;
}

.dayue-time{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20rpx;
  font-size: 26rpx;
}

.dayue-time-songda-view{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ask-for-delivery-house-view{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.payment-checked{
  background: linear-gradient(to right, #f2e1b5, #ebd198);
}

.payment-not-checked{
  background: #f0f0f0;
}

.radio-label-payment{
  padding: 8rpx 10rpx;
  font-size: 26rpx;
  border-radius: 18rpx;
  margin: 5rpx 0;
}

.pay_radio{
  display: none;
}

.radio-label-payment.flex_center.payment-checked .iconyue{
  color: black;
}

.actionItem__desc{font-size: 22rpx;color: red;}

.current-tips{
  padding: 20rpx;
  font-size: 38rpx;
  border-radius: 10rpx;
  font-weight: bold;
  color:  red;
}

.top-detail {
  margin: 20rpx;
  background-color: white;
  border-radius: 10rpx;
}

.weui-dialog.extClassIsVip{
  background-color: rgba(255,255,255,0);
}

.weui-dialog.extClassIsVip .weui-dialog__bd{
  padding: 0;
}

.close-view-class .iconfont.icon55{
  font-size: 62rpx;
  color: gainsboro;
}
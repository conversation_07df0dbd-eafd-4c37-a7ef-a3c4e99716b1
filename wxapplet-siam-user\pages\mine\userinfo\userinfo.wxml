<view>
	<view class="userinfo-item">
		<text class="title-text">头像</text>
		<!-- <image class="userinfo-avatar" src="{{userInfo.avatarUrl}}" mode="cover"></image> -->
		<image class="userinfo-avatar" src="{{data.headImg?data.headImg:'../../../assets/images/user-head.png'}}"
			mode="cover"></image>
	</view>
	<view class="userinfo-item">
		<text class="title-text">用户名</text>
		<text class="userinfo-text">{{data.username}}</text>
	</view>
	<view class="userinfo-item">
		<text class="title-text">绑定手机</text>
		<text class="userinfo-text">{{data.mobile}}</text>
	</view>
	<view class="userinfo-item" bindtap="openRealName">
		<text class="title-text">真实姓名</text>
		<view class="input-image">
			<text
				class="{{data.realName?'userinfo-text':'placeholder-text'}}">{{data.realName?data.realName:'请输入真实姓名'}}</text>
			<text class="iconfont iconhtbArrowright02"></text>
		</view>
	</view>
	<view class="userinfo-item" bindtap="openConfirm">
		<text class="title-text">性别</text>
		<view class="input-image">
			<text class="userinfo-text">{{data.sex==1?'男':data.sex==2?'女':'保密'}}</text>
			<text class="iconfont iconhtbArrowright02"></text>
		</view>
	</view>
	<view class="userinfo-item" bindtap="openEmail">
		<text class="title-text">邮箱</text>

		<view class="input-image">
			<!-- <input class="userinfo-text input-email" placeholder="请输入邮箱地址" value="{{data.email}}" bindblur="blurEmail"></input> -->
			<!-- <input placeholder-class="placeholder-email" class="userinfo-text" placeholder="请输入邮箱地址" value="{{data.email}}" disabled="disabled"></input> -->
			<text class="{{data.email?'userinfo-text':'placeholder-text'}}">{{data.email?data.email:'请输入邮箱地址'}}</text>
			<text class="iconfont iconhtbArrowright02"></text>
		</view>

	</view>
	
	<view class="userinfo-item" bindtap="{{data.isBindWx?'bindWx':''}}" data-mobile="{{data.mobile}}"
		data-isBindWx="{{data.isBindWx}}">
		<text class="title-text">绑定微信</text>
		<view class="bind-wx">
			<text class="userinfo-text">{{data.isBindWx?'已绑定':'未绑定'}}</text>
			<text class="iconfont iconhtbArrowright02"></text>
		</view>
	</view>
</view>
<mp-dialog title="选择性别" show="{{dialogShow}}" buttons="{{buttons}}" mask-closable="{{maskClosable}}"
	catchtouchmove="true" bindbuttontap="tapdialogButton">
	<radio-group bindchange="radioChange" class="sex-radio-group">
		<label wx:for="{{modeList}}" data-radioIndex="{{index}}" wx:key="index" class="radio-group-label">
			{{item.name}}
			<radio value="{{item.value}}" class="radio-group-label-radio" checked="{{item.checked}}" />
		</label>
	</radio-group>
	
</mp-dialog>
<mp-dialog title="编辑邮箱" show="{{dialogShowEmail}}" buttons="{{buttons}}" mask-closable="{{maskClosable}}"
	catchtouchmove="true" bindbuttontap="tapdialogEmailButton">
	<input class="userinfo-text input-email" placeholder="请输入邮箱地址" value="{{data.email}}"
		bindinput="bindInputEmail"></input>
</mp-dialog>
<mp-dialog title="确认真实姓名" show="{{dialogShowRealName}}" buttons="{{buttons}}" mask-closable="{{maskClosable}}"
	catchtouchmove="true" bindbuttontap="tapdialogRealNameButton">
	<view class="title-tip">请于身份证上保持一致，信息有误影响奖励提现</view>
	<input class="userinfo-text input-email" placeholder="请输入真实姓名" value="{{data.realName}}"
		bindinput="bindInputRealName"></input>
</mp-dialog>
<view class="out-login-btn theme-bg" hover-class="hover-class-public" bindtap="bindOutLogTap">
	退出登录
</view>
<view wx:if="{{menuList.length>0}}" class="mp-vtabs-view">
   <mp-vtabs vtabs="{{menuList}}" activeTab="{{activeTab}}" tabBarClass="mp-vtabs-class" bindtabclick="onTabCLick"
      bindchange="onChange" tabBarLineColor="#004ca0" tabBarActiveTextColor="#004ca0" contentHeight="{{contentHeight}}">
      <block wx:for="{{menuList}}" wx:key="title" wx:for-item="menu" wx:for-index="menuIndex">
         <mp-vtabs-content tabIndex="{{menuIndex}}">
            <view class="vtabs-content-item {{menuList.length-1==menuIndex?'is-end-item':''}}">
               <view class="commodity-type position-sticky">
                  <view class="categoryName-view out_of_range one_row">{{menu.name}}</view>
               </view>
               <block wx:for="{{menu.goodsList}}" wx:key="index" wx:for-index="goodsIndex" wx:for-item="chil">
                  <view class="commodity-item {{chil.goodsStatus==4?'isEnd':''}}"
                     hover-class="{{chil.goodsStatus==4?'':'hover-class-public'}}">
                     <view class="commodity-name-view" bindtap="{{chil.goodsStatus==4?'':'commodityDetailTap'}}"
                        data-id="{{chil.goodsId}}">
                        <image src="{{chil.mainImage?chil.mainImage:'../../assets/images/load-image.png'}}"
                           mode="aspectFill" class="commodity-image"></image>
                        <view class="sell-out" hidden="{{chil.goodsStatus!=4}}">售罄</view>
                        <view class="commodity-name-english-view">
                           <view class="commodity-name out_of_range two_row">{{chil.goodsName}}</view>
                           <view class="commodity-english"><text> </text></view>
                           <view class="money-view">￥{{chil.goodsPrice}}</view>
                        </view>
                     </view>
                  </view>
                  <view class="money-insert-view">
                     <!-- <view class="insert-view theme-bg">＋</view> -->
                     <view class='stepper' wx:if="{{chil.number>0}}">
                        <block>
                           <text class='reduce-class' bindtap='bindMinus' data-cartId="{{chil.cartId}}"
                              data-number="{{chil.number}}">－</text>
                           <input disabled type='number' value='{{chil.number}}' class='reduce-class'></input>
                           <text class="add-class" data-goodsId="{{chil.goodsId}}"
                              catchtap="{{chil.goodsStatus!=4?'openSpecifications':''}}">＋</text>
                        </block>
                     </view>
                     <block wx:else>
                        <view class="insert-view theme-bg {{chil.goodsStatus==4?'isEnd':''}}"
                           data-goodsId="{{chil.goodsId}}" bindtap="{{chil.goodsStatus!=4?'openSpecifications':''}}">＋
                        </view>
                     </block>
                  </view>
               </block>
            </view>
         </mp-vtabs-content>
      </block>
   </mp-vtabs>
</view>
<mp-halfScreenDialog show="{{specificationsDialog}}" extClass="extClassSpecifications">
   <view slot="title">选择规格</view>
   <view slot="desc">
      <view class="goods-info-view">
         <image src="{{goodsInfo.mainImage}}" mode="aspectFill" class="commodity-image"></image>
         <view>
            <view class="goods-info-name out_of_range one_row">{{goodsInfo.name}}</view>
            <view class="goods-info-specListString">已选：{{specListString}}</view>
            <view class="goods-info-price">￥{{priceAfter}}</view>
         </view>
      </view>
      <scroll-view scroll-y style="height: 56vh;">
         <view class="commdity-name-type-view">
            <view class="commdity-name">{{data.name}}</view>
            <view class="commdity-engname">{{data.name}}</view>
            <view class="commdity-type-item" wx:for="{{specList}}" wx:key="index" wx:for-index='key'>
               <view class="commdity-type-name">{{key}}</view>
               <radio-group class="radio-group" bindchange="radioChange" data-firstIndex="{{key}}">
                  <label wx:for="{{item}}" wx:key="index"
                     class="group-label theme-border {{!item.stock?'disabled-group-label':''}} {{item.checked?'active theme-bg':'theme-color-border'}} out_of_range one_row">
                     <radio value="{{index}}" checked="{{item.checked}}" disabled="{{!item.stock}}" class="radio" />
                     {{item.name}}
                  </label>
               </radio-group>
            </view>
            <is-show-tip isShow="{{specList==null}}" top="20"
			bottom="20" text="{{specList==null}}" tipText="暂无规格"></is-show-tip>
         </view>
      </scroll-view>
      <view slot="footer">
         <view class="good-choice-btn theme-bg" bindtap="insertShoppingCart">我选好了</view>
      </view>
   </view>
</mp-halfScreenDialog>
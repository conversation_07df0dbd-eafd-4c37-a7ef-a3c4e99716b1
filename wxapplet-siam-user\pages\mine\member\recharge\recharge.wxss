page {
  background: #f5f5f5;
  width: 100%;
  height: 100%;
  margin: 0;
}

.member-info{
  display: flex;
  background-color: white;
  margin: 20rpx;
  border-radius: 15rpx;
  padding: 20rpx 15rpx;
  /* align-items: center; */
  /* justify-content: space-between; */
}

.headImg{
  width: 96rpx;
  height: auto;
  border-radius: 50%;
}

.nickname-vipEndTime{
  margin-left: 20rpx;
}

.nickname{
  font-size: 30rpx;
  font-weight: bold;
  height: 50rpx;
  line-height: 50rpx;
}

.vipEndTime{
  font-size: 22rpx;
}

.ptyh-class{
  background: #8f8f8f;
  padding: 2rpx 10rpx;
  color: #f5f5f5;
  border-radius: 10rpx;
}

.cjhy-class{
  background: linear-gradient(to right, #d09650, #ba7c3f);
  padding: 2rpx 8rpx;
  color: white;
  border-radius: 10rpx;
}

.renew-botton{
  border:1rpx solid #6f4e20;
  padding: 10rpx 50rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  margin-top: 160rpx;
}

.content-recharge{
  background-color: white;
  /* margin: 20rpx; */
  /* border-radius: 15rpx; */
  padding: 20rpx 15rpx;
}

.radio-group {
  display: flex;
  /* flex-flow: row wrap;
  align-content: space-around;
  align-items: center; */
  flex-wrap: wrap;
  position: sticky;
  top: 0;
  background-color: #fff;
}

.group-label {
  width: 31%;
  margin: 1%;
  font-size: 26rpx;
  border-radius: 18rpx;
  text-align: center;
}

.disabled-group-label {
  background: #f5f5f5;
  color: #808080;
  border: none;
}

.radio {
  display: none;
}

.radio-active{
  color: white;
  background-color: inherit;
}

.label-view{
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 100%;
}

.recharge-name{
  padding-top: 20rpx;
}

.recharge-price{
  font-size: 34rpx;
  font-weight: bold;
  padding: 10rpx;
  border-radius: 0 0 15rpx 15rpx;
}

.recharge-text{
  font-size: 24rpx;
  font-weight: none;
}

.sale-name{
  text-decoration: line-through;
  margin-bottom: 20rpx;
}

.brief-description{
  font-size: 24rpx;
  border-top: 1rpx solid #f8ebe3;
  padding: 10rpx;
}

.description{
  padding: 0 10rpx;
  font-size: 28rpx;
  /* position: sticky; */
  bottom: 0;
  /* margin-top: 20rpx; */
  background-color: white;
}

.swiper-content{
  margin: 20rpx 20rpx 20rpx 20rpx;
  border-radius: 15rpx;
  background-color: white;
  height: 80%;
}

.swiper-tab {
  width: 100%;
  text-align: center;
  height: 88rpx;
  line-height: 88rpx;
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  background: #fff;
  z-index: 1;
  border-radius: 15rpx 15rpx 0 0;
  /* border-bottom: 1rpx solid #ededed; */
}

.swiper-tab-item {
  width: 50%;
}

.swiper-box {
  /* height: 400rpx; */
  background: #fff;
  border-radius: 15rpx;
}

.scroll-views {
  height: 100%;
  background: #fff;
}

.swiper-items {
  height: 100%;
  background: #fff;
  border-radius: 15rpx;
}

.swiper-current{
  background: #fff;
  border-radius: 15rpx;
}

.my-record-swiper{
  margin: 20rpx;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  background-color: #f9fafe;
  border-radius: 15rpx;
}

.balance-swiper{
  margin: 20rpx;
  padding: 20rpx;
  background-color: #f9fafe;
  border-radius: 15rpx;
  text-align: center;
}

.select-balance{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 30rpx 50rpx;
}

.lijichaxun-view{
  display: flex;
  align-items: center;
  justify-content: center;
}

.lijichaxun{
  width: 30%;
  border-radius: 50rpx;
  padding: 10rpx 30rpx;
}

.balance-button{
  padding: 0 20rpx;
}

.balance-tip{
  font-size: 32rpx;
  font-weight: bold;
}

.iconyue{
  color: #f0dcab;
}

.balance-value{
  font-size: 30rpx;
  font-weight: bold;
}

.balance-title{
  font-size: 28rpx;
}

.ckwdjl-view{
  font-size: 34rpx;
  font-weight: bold;
}

.zdjl-czjl-view{
  color: #a2a3a7;
  font-size: 28rpx;
}

.ljck-button{
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5rpx 30rpx;
  border-radius: 50rpx;
  margin-top: 40rpx;
  width: 60%;
}

.bottom-view{
  position: fixed;
  bottom: 0;
  width: 100%;
} 

.total-view{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20rpx 30rpx;
  border-radius: 50rpx;
  padding: 20rpx;
  font-size: 30rpx;
  background-color: #2b2f3a;
  color: white;
}


.pay-button{
  background: linear-gradient(to right, #eeb28d, #fbdbbf);
  padding: 10rpx 30rpx;
  border-radius: 50rpx;
  color: #652800;
  font-weight: bold;
  font-size: 30rpx;
}

.symbol-text{
  font-size: 24rpx;
  color: #fac9ae;
}

.total-text{
  font-size: 32rpx;
  color: #fac9ae;
  font-weight: bold;
  margin-right: 20rpx;
}

.sale-price{

}
.input-phone-number{
  text-align: center;
}

.brand-icon{
  width: 208rpx;
  height: auto;
  margin-top: 88rpx;
}

.input-button-view{
  margin: 90rpx;
  text-align: left;
}

.input-button-code{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30rpx;
}

.input-button-code input{
  width: 70%;
}

.get-verification-code{
  width: 25%;
  font-size: 22rpx;
  border-radius: 50rpx;
  padding: 10rpx;
  /* height: 30rpx; */
  line-height: 30rpx;
  text-align: center;
}

/* .code-disabled-false{
  border: 0.5rpx solid #acacac;
  color: #acacac;
} */

.code-disabled-false{
  background: #f5f5f5;
  color: #808080;
  border: 1rpx solid #808080;
}

input{
  height: 98rpx;
}

.confirm-btn{
  margin-top: 50rpx;
  color: white;
}


button {
  padding-left: 0px;
  padding-right: 0px;
  margin-left: 0px;
  margin-right: 0px;
}

button[plain] {
  color: black;
  border: 0px;
  font-size: 30rpx;
}
<view class="section" wx:if="{{userLocation}}">
	<picker mode="region" bindchange="bindRegionChange" value="{{region}}" custom-item="{{customItem}}">
		<view class="picker flex_center">
			{{region[1]?region[1]:"定位中..."}}<text class="iconfont iconweibiaoti35" wx:if="{{region[1]}}"></text>
		</view>
	</picker>
	<input bindinput="bindInput" placeholder="请输入地址名称	{{scope.userLocation}}" focus="true" />
</view>
<view>
	<scroll-view scroll-y="true" style="height:{{winHeight}}px">
		<view bindtap="bindSearch" data-keywords="{{i}}" class="text_box" wx:for="{{tips}}" wx:for-item="i" wx:key="index">
			<view class="address-name">
				{{i.name}}
			</view>
			<view class="address-address">
				{{i.address}}
			</view>
		</view>
	
		<is-show-tip isShow="{{tips.length<=0}}" bind:bindTap="openSettingInfo" buttonText="位置授权" top="40" bottom="10" text="{{tips.length<=0}}" tipText="没有搜索到地址" button="{{!userLocation}}"></is-show-tip>
	</scroll-view>
</view>
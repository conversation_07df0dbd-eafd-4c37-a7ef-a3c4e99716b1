page {
  background: #f5f5f5;
  padding-bottom: 160rpx;
}

.carousel-swiper{
  margin:20rpx;
  height: 31vh;
}

.carousel-swiper-item{
  border-radius: 15rpx;
  height:100%;
}

.carousel-image{
  width: 100%;
  height: 100%;
  border-radius: 15rpx;
}

.commdity-xuanze{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.commodity-detail-view {
  background: white;
  margin-top: 20rpx;
  padding: 20rpx;
  margin: 20rpx;
  border-radius: 20rpx;
}

.detail-title {
  font-size: 30rpx;
  line-height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.commodity-detail {
  font-size: 26rpx;
  color: #919191;
}

.shopping-detail-view {
  position: fixed;
  bottom: 0;
  width: 100%;
}

.buy-shopping-cart-view {
  width: 100%;
  display: flex;
  height: 100rpx;
}

.immediate-purchase {
  width: 50%;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.add-to-cart {
  width: 50%;
  font-size: 30rpx;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.commodity-shopping-view {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx 20rpx;
  background: white;
  box-shadow: -2px 0px 5px 0.5px rgba(0, 0, 0, 0.1);
}

.commodity-name-price-view {
  display: flex;
  flex-direction: column;
}

.commodity-name-price {
  display: flex;
  align-items: center;
}

/*主容器*/
.stepper {
  width: auto;
  height: auto;
}

/*加号和减号*/
.stepper text {
  width: 48rpx;
  height: 48rpx;
  line-height: 48rpx;
}

/*数值*/
.stepper input {
  width: 40px;
}

.money-icon-total-price {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}

.money-icon {
  font-size: 30rpx;
  margin-left: 30rpx;
}

.baozhuangfei-class {
  font-size: 24rpx;
  line-height: 40rpx;
  display: flex;
  align-items: center;
}

/* hover-class样式，点击态 */
.hover-class-public {
  opacity: 0.9;
}

.money-icon-fullPrice{
  font-weight: bold;
}

.full-reduction-view {
  font-size: 20rpx;
  font-weight: bold;
  padding: 0 10rpx;
  margin: 0 10rpx;
  border-radius: 10rpx;
}

.fullPriceReductionClass {
  color: gainsboro;
  display: flex;
  align-items: center;
  text-decoration: line-through;
}

/* shopcart 样式 */
.content-fullReductionRuleName {
  width: 100%;
  background: #fffadc;
  font-size: 24rpx;
  text-align: center;
  position: sticky;
  top: 0;
  z-index: 9999;
  opacity: 0.7;
  visibility: visible;
}

.insert-view {
  border-radius: 50%;
  width: 38rpx;
  height: 38rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.shopping-cart-detail {
  position: fixed;
  bottom: 0;
  z-index: 9999;
  width: 100%;
}

.highlight {
  position: relative;
  top: -10px;
  width: 80rpx;
  line-height: 80rpx;
  text-align: center;
  height: 80rpx;
  margin-left: 20rpx;
  margin-right: 20rpx;
  border-radius: 50%;
  border: 10rpx solid #444444;
}

.shopping-cart-content {
  width: 100%;
  display: flex;
  align-items: center;
  background: #535257;
  z-index: 9999;
  height: 54px;
}

.shopping-cart-left {
  width: 70%;
  height: 100%;
  display: flex;
  align-items: center;
  background: #505052;
  z-index: 9999;
}

.shopping-cart-right {
  width: 30%;
  height: 100%;
  text-align: center;
  color: white;
  z-index: 9999;
  font-size: 30rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.shopping-cart-bg{
  background: #535257;
}

.shopping-cart-desc {
  font-size: 24rpx;
  color: #8c8c8e;
}

.shopping-cart-totalPrice {
  display: flex;
  align-items: center;
}

.fullPriceReductionClass {
  color: gainsboro;
  text-decoration: line-through;
  margin-right: 20rpx;
}

.totalPrice {
  color: white;
  font-weight: bold;
}

.full-price-reduction {
  color: white;
  font-weight: bold;
}

.not-full-price-reduction {
  font-size: 28rpx;
  color: #8c8c8e;
}

.fullPriceReductionIsHidden {
  font-size: 28rpx;
}

.content-manjian {
  position: relative;
  top: 0;
  background: #fffadc;
  font-size: 24rpx;
  font-weight: bold;
  text-align: center;
  height: 73px;
  z-index: 9999;
}

.num {
  position: absolute;
  top: -6px;
  right: -10px;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 16px;
  font-size: 9px;
  font-weight: 700;
  color: #fff;
  background: rgb(240, 20, 20);
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.4);
}

.business-recommend-scroll-view {
  height: 269rpx;
  white-space: nowrap;
}

.icon-business-recommend-class {
  width: 100%;
  height: 200rpx;
  border-radius: 15rpx 15rpx 0 0;
}
.business-recommend-title {
  font-size: 34rpx;
  font-weight: bold;
  margin: 20rpx 20rpx 0 20rpx;
}

.business-recommend-scroll-view {
  margin: 20rpx 0rpx;
  background: #fff;
  border-radius: 50rpx;
  height: 100%;
  display: flex;
  align-items: center;
}

.business-recommend-scroll-view scroll-view {
  display: block;
  width: 100%;
}

.business-recommend-items {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.business-recommend-item {
  width: 32%;
  height: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background: #f5f5f5;
  border-radius: 15rpx;
  margin: 0 10rpx;
}

.item-two {
  margin: 0 3.5%;
}

.business-recommend-detail-view {
  width: 100%;
  height: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.fullname-class {
  margin-top: 11rpx;
  font-size: 26rpx;
  width: 70%;
}

.engname-class {
  font-size: 24rpx;
  color: #ccc;
  width: 90%;
}

.business-recommend-money-view {
  width: 90%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0 10rpx 0;
}

.business-recommend-money {
  font-size: 26rpx;
  font-weight: bold;
}

.plus-view {
  font-size: 28rpx;
  width: 38rpx;
  height: 38rpx;
  line-height: 38rpx;
  text-align: center;
  border-radius: 50%;
  color: white;
}

.settlement-view {
  position: fixed;
  z-index: 999;
  background: white;
  top: 0;
  border-bottom: 6rpx solid #f5f5f5;
}

.goods-info-view {
  display: flex;
  padding-bottom: 20rpx;
  padding-left: 20rpx;
}

.goods-info-name {
  font-size: 30rpx;
  font-weight: bold;
}

.goods-info-specListString {
  color: #6b6b6b;
  font-size: 24rpx;
}

.goods-info-price {
  font-weight: bold;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  color: #e0583b;
}

.specifications-scroll-view {
  height: 274px;
}

.clearNull {
  font-weight: 700;
  font-size: 15px;
  color: #80858a;
  
}

.specifications-dialog {
  margin-bottom: 0rpx;
}

.commdity-name-type-view {
  padding: 20rpx;
  background: #fff;
  border-radius: 20rpx;
  margin: 0 20rpx;
}

.commdity-name {
  font-size: 32rpx;
  font-weight: bold;
  line-height: 50rpx;
  width: 70%;
  align-items: flex-start;
}

.commdity-name .name{
  font-size: 30rpx;
}

.commdity-name .price{
  font-size: 32rpx;
  font-weight: bold;
}

.commdity-engname {
  font-size: 28rpx;
  line-height: 60rpx;
}

.commdity-type-item {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: left;
  align-items: center;
  flex-wrap: wrap;
  padding-bottom: 10rpx;
}

.commdity-type-name {
  font-size: 28rpx;
  margin-right: 30rpx;
}

.radio-group {
  width: 80%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  padding: 10rpx;
  background: #fff;
  border-radius: 50rpx;
}

.group-label {
  width: 28%;
  padding: 1%;
  margin: 1%;
  font-size: 26rpx;
  border-radius: 18rpx;
  text-align: center;
}

.disabled-group-label {
  background: #f5f5f5;
  color: #808080;
  border: none;
}

.radio {
  display: none;
}

/* 商品信息 */
.commodity-item-view {
  display: flex;
  flex-direction: column;
  /* margin-bottom: 10rpx; */
  background: white;
  border-radius: 10rpx;
  padding-top: 20rpx;
}

.commodity-type {
  /* line-height: 64rpx; */
  padding: 10rpx 20rpx;
}

.categoryName-view {
  /* width: 35%; */
  font-size: 28rpx;
}

.font-white {
  color: white;
}

.commodity-item {
  display: flex;
  padding: 20rpx 20rpx 0rpx 20rpx;
  align-items: center;
  border-radius: 5rpx;
}

.commodity-image {
  width: 170rpx;
  height: 152rpx;
  border-radius: 8rpx;
  margin-right: 10rpx;
}

.commodity-name-view {
  width: 100%;
  display: flex;
  align-items: center;
}

.line-view {
  background: #b0b0b0;
  width: 100%;
  height: 2rpx;
}

.commodity-name {
  font-size: 28rpx;
  color: #969696;
  font-weight: bold;
}

.commodity-english {
  font-size: 24rpx;
  color: #b0b0b0;
}

.money-view {
  font-size: 30rpx;
  font-weight: bold;
  margin-top: 30rpx;
}

.insert-view {
  border-radius: 50%;
  width: 45rpx;
  height: 45rpx;
  line-height: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.money-insert-view {
  /* width: 100%; */
  position: absolute;
  /* margin-left: 85%;
  margin-top: 10%; */
  margin-top: -10%;
  right: 5%;
}

#space-view {
  height: 20rpx;
  background: white;
}

.now-order-image {
  width: 100%;
  height: 100%;
}

/* shopcart 样式 */
.content-fullReductionRuleName {
  width: 100%;
  background: #fffadc;
  font-size: 24rpx;
  text-align: center;
  position: sticky;
  top: 0;
  z-index: -1;
  opacity: 0.7;
  visibility: visible;
}

.shopping-cart-detail {
  position: fixed;
  bottom: 0;
  z-index: 9999;
  width: 100%;
  background-color: white;
}

.highlight {
  position: relative;
  top: -10px;
  width: 80rpx;
  line-height: 80rpx;
  text-align: center;
  height: 80rpx;
  margin-left: 20rpx;
  margin-right: 20rpx;
  border-radius: 50%;
  border: 10rpx solid #444444;
}

.shopping-cart-content {
  width: 100%;
  display: flex;
  align-items: center;
  background: #535257;
  z-index: 9999;
  height: 54px;
}

.shopping-cart-left {
  width: 70%;
  height: 100%;
  display: flex;
  align-items: center;
  background: #505052;
  z-index: 9999;
}

.shopping-cart-right {
  width: 30%;
  height: 100%;
  text-align: center;
  color: white;
  z-index: 9999;
  font-size: 30rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.shopping-cart-bg{
  background: #535257;
}

.shopping-cart-desc {
  font-size: 24rpx;
  color: #8c8c8e;
}

.shopping-cart-totalPrice {
  display: flex;
  align-items: center;
}

.fullPriceReductionClass {
  color: gainsboro;
  text-decoration: line-through;
  margin-right: 20rpx;
}

.totalPrice {
  color: white;
  font-weight: bold;
}

.full-price-reduction {
  color: white;
  font-weight: bold;
}

.not-full-price-reduction {
  font-size: 28rpx;
  color: #8c8c8e;
}

.fullPriceReductionIsHidden {
  font-size: 28rpx;
}

.content-manjian {
  position: relative;
  top: 0;
  background: #fffadc;
  font-size: 24rpx;
  font-weight: bold;
  text-align: center;
  height: 73px;
  z-index: 9999;
}

.num {
  position: absolute;
  top: -6px;
  right: -10px;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 16px;
  font-size: 9px;
  font-weight: 700;
  color: #fff;
  background: rgb(240, 20, 20);
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.4);
}

.business-recommend-scroll-view {
  height: 269rpx;
  white-space: nowrap;
}

.icon-business-recommend-class {
  width: 100%;
  height: 200rpx;
  border-radius: 15rpx 15rpx 0 0;
}

.business-recommend-title {
  font-size: 34rpx;
  font-weight: bold;
  margin: 20rpx 20rpx 0 20rpx;
}

.business-recommend-scroll-view {
  margin: 20rpx 0rpx;
  background: #fff;
  border-radius: 50rpx;
  height: 100%;
  display: flex;
  align-items: center;
}

.business-recommend-scroll-view scroll-view {
  display: block;
  width: 100%;
}

.business-recommend-items {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.business-recommend-item {
  width: 32%;
  height: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background: #f5f5f5;
  border-radius: 15rpx;
  margin: 0 10rpx;
}

.item-two {
  margin: 0 3.5%;
}

.business-recommend-detail-view {
  width: 100%;
  height: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.fullname-class {
  margin-top: 11rpx;
  font-size: 26rpx;
  width: 70%;
}

.engname-class {
  font-size: 24rpx;
  color: #ccc;
  width: 90%;
}

.business-recommend-money-view {
  width: 90%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0 10rpx 0;
}

.business-recommend-money {
  font-size: 26rpx;
  font-weight: bold;
}

.plus-view {
  font-size: 28rpx;
  width: 38rpx;
  height: 38rpx;
  line-height: 38rpx;
  text-align: center;
  border-radius: 50%;
  color: white;
}

.settlement-view {
  position: fixed;
  z-index: 999;
  background: white;
  top: 0;
  border-bottom: 6rpx solid #f5f5f5;
}

.manjiantop {
  position: absolute;
  top: 0;
}

.closeImages {
  position: relative;
  left: 91%;
  top: 4%;
  z-index: 999;
}

.close-image-class {
  width: 50rpx;
  height: auto;
}

.specifications-scroll-view {
  height: 274px;
}

.go-to-shop{
  padding: 0 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  margin:0;
  margin-left: 20rpx;
}

.stepper-view{
  margin:20rpx 0 10rpx 0;
}

.clearNull {
  font-weight: 700;
  font-size: 15px;
  color: #80858a;
}

.specifications-dialog {
  margin-bottom: 0rpx;
}

.good-choice-view {
  padding: 20rpx;
  border-top: 1prx #808080 solid;
}

.good-choice-btn {
  width: 100%;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.shoppingCart-screen-dialog {
  height: 55%;
}

.shoppingCart-scroll-view {
  height: 350px;
}

.shoppingCart-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* width: 100%; */
  padding: 20rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.goodsName-restructure-view {
  width: 50%;
}

.goodsName-packingCharges {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
}

.goodsName {
  font-size: 28rpx;
}

.restructure {
  font-size: 22rpx;
  color: #808080;
}

.goodsPrice-number-view {
  width: 50%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.goodsPrice {
  font-size: 30rpx;
  font-weight: bold;
  color: #f01414;
}

/*主容器*/

.stepper {
  margin-left: 20rpx;
  display: flex;
  align-items: center;
}

/*加号和减号*/

.stepper text {
  width: 45rpx;
  height: 45rpx;
  line-height: 45rpx;
  font-size: 28rpx;

}

/*数值*/

.stepper input {
  width: 24px;
}

/* 商家栏样式 */
.swiper-bussiness {
  text-align: left;
}

.swiper-tab-bussiness {
  text-align: left;
}

.swiper-bussiness-item {
  background: white;
  margin-bottom: 20rpx;
  padding: 20rpx;
}

.swiper-bussiness-info {
  font-size: 28rpx;
  color: #717171;
}

.swiper-bussiness-title {
  font-size: 30rpx;
  font-weight: bold;
}

.swiper-bussiness-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15rpx 0;
}

.swiper-bussiness-row-left {
  width: 20%;
  font-size: 26rpx;
  color: black;
  font-weight: bold;
}

.contact-bussiness-text {
  color: #2e87cd;
}

.carousel-swiper-item {
  border-radius: 15rpx;
  height: 200rpx;
}

.carousel-image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
}

/* 自定义弹出框的最大高度为100%，并设置他的左右上交的border-ric为0 */
.weui-show .weui-half-screen-dialog.extClassShoppingCart {
  max-height: 100vh;
  padding: 0 20rpx;
  position: fixed;
  bottom: 0;
  padding-bottom: 12%;
}

.weui-half-screen-dialog.extClassShoppingCart .weui-half-screen-dialog__ft {
  padding: 20rpx 0;
  position: sticky;
  bottom: 0;
}


.weui-show .weui-half-screen-dialog.extClassShoppingCart .weui-half-screen-dialog__hd {
  padding: 0 20rpx;
}

/* 选择商品规格弹窗 */
.weui-show .weui-half-screen-dialog.extClassSpecifications {
  z-index: 9999999;
}

/* 自定义弹窗样式 */
.weui-show .weui-half-screen-dialog.extClassSpecifications {
  padding: 0 20rpx;
}

.weui-half-screen-dialog.extClassSpecifications .weui-half-screen-dialog__ft {
  padding: 20rpx 0 0 0;
  position: sticky;
  bottom: 0;
}

/* 自定义弹出框的最大高度为100%，并设置他的左右上交的border-ric为0 */
.weui-show .weui-half-screen-dialog.extClassSpecifications {
  max-height: 90vh;
  /* border-radius: 0%; */
}

.weui-show .weui-half-screen-dialog.extClassSpecifications .weui-half-screen-dialog__hd {
  padding: 0 20rpx;
}

.vtabs-content-item {
  height: 100%;
}

.mp-vtabs-class scroll-view {
  height: 81vh;
  padding-bottom: 11vh;
  background: #eeeeee;
}

.weui-vtabs-content__wrp scroll-view {
  height: 81vh;
  padding-bottom: 11vh;
}

.weui-vtabs-bar__scrollview .weui-vtabs-bar__content {
  padding-bottom: 100px;
}

.weui-vtabs-content__scrollview .weui-vtabs-content {
  padding-bottom: 50px;
}

.is-end-item {
  padding-bottom: 50px;
}

.theme-other-bg {
  background: #353535;
  color: #5f5e63;
}

.weui-vtabs-bar__item .weui-vtabs-bar__title {
  white-space: normal;
  text-align: center;
}

.evaluate-business-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.evaluate-info-left {
  width: 80%;
  margin: 20rpx 0rpx;
  padding: 0 50rpx;
  border-right: 1rpx solid #f5f5f5;
}

.evaluate-info-right {
  width: 20%;
  padding: 20rpx;
  text-align: center;
}

.business-evaluate {
  display: flex;
  justify-content: space-between;
}

.evaluate-total-score {
  color: #f56427;
  font-size: 40rpx;
  margin-right: 25rpx;
}

.evaluate-total-star {
  color: #4f4f4f;
  font-size: 24rpx;
}

.evaluate-total-num {
  color: #4f4f4f;
  font-size: 35rpx;
}

.view-line {
  height: 20rpx;
  background: #f3f3f3;
}

.evaluate-items {
  padding: 20rpx;
}

.evaluate-item {
  display: flex;
  justify-content: space-between;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.evaluate-item-detail{
  width: 90%;
}

.evaluate-user-image {
  width: 8%;
  height: 8%;
  border-radius: 50%;
}

.evaluate-itemu-username-time {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.username-detail{
  font-size: 28rpx;
}

.images-url{
  width: 100rpx;
  height: 100rpx;
}

.datetime-detail{
  font-size: 24rpx;
  color: #7d7d7d;
}

.appraise-class-item{
  width: 30rpx;
  height: 30rpx;
}

.pl-dz-class{
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: end;
}

.appraise-class-pl-dz{
  margin-left: 10rpx;
}

.pl-dz-view{
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #9b9b9b;
}

.navigator-class {
  padding: 15rpx;
  background: white;
  margin: 0 20rpx;
  border-radius: 20rpx;
}

.invite-wrapper {
  padding: 10rpx;
  height: 200rpx;  
}

.invite-image {
  width: 100%;
  height: 100%;
}

.view-line{
  height: 20rpx;
}

.isOutofDeliveryRange{
  opacity: 0.4;
}

.icongouwuche1{
  font-size: 40rpx;
}

.collect-in{
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 28%;
}

.iconshoucang1{
  color: #c3c3c3;
  font-size: 45rpx;
}

.is-collect{
  color: #fc7a7c;
}

.shoucang-text{
  font-size: 26rpx;
}

.not-collect{
  color: #c3c3c3;
}

.score-money{
  font-size: 30rpx;
}
<view class="refund-process-top">
	<view class="refund-process-title">退款流程</view>
	<view class="view-line"></view>
	<view class="refund-process-details">
		<view class="refund-process-item {{orderRefundProcessList.length-1!=index?'refund-process-item-margin':''}}" wx:for="{{orderRefundProcessList}}" wx:key="index">
			<view class="seria-number {{index==0?'theme-bg':'detail-out out-status'}}">{{orderRefundProcessList.length-index}}</view>
			<view class="detail">
				<view class="detail-top">
					<text class="detail-status {{index==0?'theme-color':'detail-out'}}">{{item.name}}</text>
					<text class="detail-time">{{item.createTime}}</text>
				</view>
				<view class="detail-tip" wx:if="{{item.description}}">{{item.description}}</view>
			</view>
		</view>
	</view>
</view>
<view class="refund-process-top">
	<view class="refund-process-title">退款信息</view>
	<view class="view-line"></view>
	<view class="order-detail-lists">
		<view class="order-detail-list" wx:for="{{orderRefundGoodsList}}" wx:key="index">
			<image src="{{item.mainImage}}" mode="scaleToFill" class="order-mainImage"></image>
			<view class="order-detail">
				<view class="order-goods-name">
					<view class="out_of_range one_row">{{item.goodsName}}</view>
					<view class="order-price">￥{{item.price}}</view>
				</view>
				<view class="order-specListAnalysis">
					<view class="out_of_range one_row">{{item.specListAnalysis}}</view>
					<view> x{{item.number}} </view>
				</view>
			</view>
		</view>
	</view>
	<view class="refund-process-column">
		<text class="title">配送费</text>
		<text class="pirce">￥{{orderRefund.deliveryFee}}</text>
	</view>
	<view class="view-line"></view>
	<view class="refund-process-column">
		<text class="title">包装费</text>
		<text class="pirce">￥{{orderRefund.packingCharges}}</text>
	</view>
	<view class="view-line"></view>
	<view class="refund-process-column">
		<text class="title">退款金额</text>
		<view class="refund-process-column-right">
			<text class="refund-process-column-tip" decode="true">实退&nbsp;</text>
			<text class="pirce">￥{{orderRefund.refundAmount}}</text>
		</view>
	</view>
	<view class="view-line"></view>
	<view class="refund-process-column">
		<text class="title">退单原因</text>
		<text class="refund-process-column-tip">{{orderRefund.refundReasonText}}{{orderRefund.refundReasonDescription?'-'+orderRefund.refundReasonDescription:''}}</text>
	</view>
	<view class="view-line"></view>
	<view class="refund-process-column">
		<text class="title">退款账户</text>
		<text class="refund-process-column-tip">{{orderRefund.refundAccountText}}</text>
	</view>
</view>
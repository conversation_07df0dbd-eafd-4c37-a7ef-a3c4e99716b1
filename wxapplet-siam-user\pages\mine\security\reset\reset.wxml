<view class="top-class">
  <view class="account-phone">
    请为账号 {{userInfo.mobile}}
  </view>
  <view class="account-tip">{{!isAgain?"设置6位数支付密码":"请再次输入密码"}}</view>
</view>
<view class='password_dialog_main'>
  <view class='password_dialog_tip'><text>使用会员卡余额支付需要验证身份，验证通过后才可进行支付。</text></view>
  <view class='password_dialog_row theme-border' catchtap='getFocus'>
    <view class='password_dialog_item_input' wx:for='{{6}}' wx:key='item' wx:for-index='i'>
      <text wx:if='{{pwdVal.length>i}}'></text>
    </view>
  </view>
  <input class='password_dialog_input_control' password type='number' focus='{{payFocus}}' 
  bindinput='inputPwd' maxlength='6' value="{{pwdVal}}" adjust-position="{{adjustPosition}}"/>
</view>

<!-- 密码输入框 -->
<view wx:if='{{showPayPwdInput}}'>
    <view class='bg_layer'></view>
    <view class='password_dialog_main'>
        <view class='input_title'>
            <view class='input_back' catchtap='hidePayLayer'><text></text></view>
            <text>输入支付密码</text>
        </view>
        <view class='password_dialog_tip'><text>使用会员卡余额支付需要验证身份，验证通过后才可进行支付。</text></view>
        <view class='password_dialog_row' catchtap='getFocus'>
            <view class='password_dialog_item_input' wx:for='{{6}}' wx:key='item' wx:for-index='i'>
                <text wx:if='{{pwdVal.length>i}}'></text>
            </view>
        </view>
        <view class='password_dialog_forget_pwd' catchtap='hidePayLayer'>忘记密码</view>
        <input class='password_dialog_input_control' password type='number' focus='{{payFocus}}' bindinput='inputPwd' maxlength='6'/>
    </view>
</view>
<view class="page-content">
  <view class="member-center">
    <view class="member-vip-info">
      <view class="vip-bg-view">
        <view class="flex_between">
          <view class="member-info">
            <image src="{{data.headImg}}" mode="widthFix" class="headImg"></image>
            <view class="nickname-vipEndTime">
              <view class="nickname flex_start">
                <view class="out_of_range one_row">{{data.username}}</view>
                <view class="vip-image-view">
                  <image wx:for="{{isVipImages}}" wx:key="index" src="{{item}}?v={{appVersion}}" mode="widthFix"
                    class="is-vip-image"></image>
                </view>
              </view>
              <view class="vip-recharge flex_between">
                <!-- <view class="vipEndTime {{data.type==1?'ptyh-class':'cjhy-class'}}">
                  {{data.typeVipText}}
                </view> -->

                <view class="renew-botton" wx:if="{{data.type==1}}" bindtap="bindVipRecharge">
                  加入会员</view>
                <view class="renew-botton" wx:else bindtap="bindVipRecharge">去续费</view>
              </view>
              <view class="no-vipno flex_start">ID：{{data.vipNo}}</view>
            </view>
          </view>
          <view bindtap="bindReward" class="wodejl-view">佣金明细></view>
        </view>
        <view>
          <!-- <view class="vip-recharge-view flex_between">
            <view class="renew-botton" wx:if="{{data.vipStatus!=3&&data.vipStatus!=2}}" bindtap="bindVipRecharge">会员充值
            </view>
            <view class="renew-botton" wx:if="{{data.vipStatus==3}}">立即续费</view>
            <text class="iconfont iconhuiyuan1"></text>
          </view>-->
          <view class="mine-blocking-view">
            <view class="mine-blocking">
              <navigator class="mine-navigator" url="../../balance/index/index">
                <view class="number-tip theme-color">{{data.balance}}</view>
                <view class="text-tip">余额</view>
              </navigator>
              <navigator class="mine-navigator" url="../../integral/integral">
                <view class="number-tip theme-color">{{data.points}}</view>
                <view class="text-tip">我的积分</view>
              </navigator>
              <navigator class="mine-navigator" url="../../share/reward/reward">
                <view class="number-tip theme-color">{{data.inviteRewardAmount}}</view>
                <view class="text-tip">佣金</view>
              </navigator>
              <navigator class="mine-navigator" url="../../coupons/coupons">
                <view class="number-tip theme-color">{{data.couponsNumber?data.couponsNumber:0}}</view>
                <view class="text-tip">卡券</view>
              </navigator>
            </view>

          </view>

        </view>

      </view>

    </view>

  </view>
  <view class="add_buttom">
    <view class="add-vip" bindtap="bindVipRecharge">加入会员尊享4大权益</view>
    <!-- <image src="https://shop-beingwell.oss-cn-hangzhou.aliyuncs.com/data/images/business/vip_page/benefits_icon.png"
								mode="aspectFill" class="vip-button-image"></image> -->
  </view>

  <view class="vip-details-view">
    <view class="vip-detail">
      <!-- <text class="iconfont icondingdan-"></text> -->
      <image src="https://shop-beingwell.oss-cn-hangzhou.aliyuncs.com/data/images/business/vip_page/guarantee.png?v={{appVersion}}"
        mode="aspectFill" class="invite-image"></image>
      <view class="iconfont-title">账户保障</view>
    </view>
    <view class="vip-detail">
      <!-- <text class="iconfont iconyue"></text> -->
      <image src="https://shop-beingwell.oss-cn-hangzhou.aliyuncs.com/data/images/business/vip_page/wallet.png?v={{appVersion}}"
        mode="aspectFill" class="invite-image"></image>
      <view class="iconfont-title">独家礼券</view>
    </view>
    <view class="vip-detail">
      <!-- <text class="iconfont iconyouhuiquan"></text> -->
      <image src="https://shop-beingwell.oss-cn-hangzhou.aliyuncs.com/data/images/business/vip_page/trophy.png?v={{appVersion}}"
        mode="aspectFill" class="invite-image"></image>
      <view class="iconfont-title">下单奖励</view>
    </view>
    <view class="vip-detail">
      <!-- <text class="iconfont iconyouhuiquan"></text> -->
      <image src="https://shop-beingwell.oss-cn-hangzhou.aliyuncs.com/data/images/business/vip_page/share.png?v={{appVersion}}"
        mode="aspectFill" class="invite-image"></image>
      <view class="iconfont-title">邀请奖励</view>
    </view>
  </view>
  
  <view class="gift-view">
    <view class="view-line"></view>
    <view class="gift-view-info">
      <image src="https://shop-beingwell.oss-cn-hangzhou.aliyuncs.com/data/images/business/vip_page/recharge.png?v={{appVersion}}"
      mode="widthFix" class="gift-image" bindtap="bindVipRecharge"></image>
    </view>
    <view class="gift-content">
      <view>
        <view class="mrwcyqrw">每日完成邀请任务</view>
        <view class="stqdsh">送TA券的时候你还有现金奖励</view>
      </view>
      <view class="goto-finish" bindtap="bindInvite">去完成</view>
    </view>
    <view class="view-line"></view>
    <view class="vip-rules">
      <text class="vip-text">{{current.vipRule}}</text>
    </view>
  </view>
</view>
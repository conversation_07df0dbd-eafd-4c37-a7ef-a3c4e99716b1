page {
  background: #f5f5f5;
  width: 100%;
  height: 100%;
  margin: 0;
}

.topImg {
  width: 100%;
  height: auto;
}

.index-bg-class {
  width: 100%;
  border-radius: 0 0 35% 35%;
}

.balance-views {
  height: 360rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  color: white;
}

.balance-view {
  font-size: 60rpx;
}

.balance-text {
  font-size: 28rpx;
}

.balance-detail{
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 80%;
  margin-top: 20rpx;
}

.balance-detail-info{
  text-align: center;
}

.balance-quota{
  font-size: 32rpx;
}

.balance-title{
  font-size: 22rpx;
}

.swiper-tab {
  width: 100%;
  text-align: center;
  height: 88rpx;
  line-height: 88rpx;
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  background: #fff;
  z-index: 1;
  border-bottom: 1rpx solid #ededed;
}

.swiper-tab-item {
  width: 50%;
}

.swiper-box {
  display: block;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: absolute;
}

.swiper-items {
  height: 100%;
}

.scroll-views {
  height: 100%;
}

.integral-items {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 26rpx;
  border-bottom: 0.5px solid #f5f5f5;
  margin: 20rpx;
  background-color: white;
  border-radius: 15rpx;
}

.integral-item-name {
  font-size: 28rpx;
  color: rgb(0, 0, 0);
  margin-bottom: 10rpx;
}

.integral-item-time {
  font-size: 26rpx;
  color: rgb(141, 141, 141)
}

.integral-item-number {
  font-size: 34rpx;
}

.color-integral {
  color: red;
}

.order-title{
  font-size: 28rpx;
  color: rgb(141, 141, 141);
}

.order-info{
  font-size: 28rpx;
  margin-bottom: 10rpx;
}
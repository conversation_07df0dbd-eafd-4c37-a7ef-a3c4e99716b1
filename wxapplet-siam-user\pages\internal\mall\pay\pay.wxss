page {
  background: #f5f5f5;
  padding-bottom: 99rpx;
}

.please-address{
  font-size: 32rpx;
  font-weight: bold;
  color: gainsboro;
}

.margin-border-radius{
  margin: 20rpx;
  border-radius: 15rpx;
}

.ask-for-delivery-house-view{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  width: 100%;
}

.ask-for-delivery-address-detail {
  font-size: 28rpx;
  height: 40rpx;
  line-height: 40rpx;
  color: #7f7f7f;
  display: flex;
  align-items: center;
}

.ask-for-delivery-view{
  width: 90%;
}

.ask-for-delivery-detail {
  margin-top: 20rpx;
  padding: 20rpx;
  background: white;
}

.ask-for-delivery-title {
  font-size: 32rpx;
  font-weight: bold;
  line-height: 60rpx;
}

.ask-for-delivery {
  display: flex;
  align-items: center;
}

.ask-for-delivery-address {
  width: 75%;
}

.ask-for-delivery-house {
  font-size: 28rpx;
  display: flex;
  align-items: center;
}

.right-class{
  width: 100%;
  margin-left: 10rpx;
}

.shopping-commodity-details {
  margin-top: 10rpx;
  padding: 20rpx;
  background: white;
}

.commodity-name-price-detail {
  padding: 10rpx 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.commodity-name-types {
  width: 70%;
}

.commodity-name {
  font-size: 30rpx;
  font-weight: bold;
}

.commodity-types {
  font-size: 30rpx;
  color: #7f7f7f;
}

.commodity-totalnum {
  font-size: 30rpx;
}

.commodity-price {
  font-size: 28rpx;
  font-weight: bold;
  margin-left: 20rpx;
}

.commodity-totalnum-price {
  width: 25%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.total-money-view {
  width: 100%;
  line-height: 40rpx;
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.total-title {
  font-size: 28rpx;
}

.total-money {
  font-size: 32rpx;
  font-weight: bold;
  margin-left: 20rpx;
  width: 40%;
}

.icon-wechat-pay {
  width: 50rpx;
  height: auto;
  margin-right: 20rpx;
}

.pay-mode-view {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  margin-top: 20rpx;
  padding: 20rpx;
  font-size: 30rpx;
}

.pay-mode-title{
  width: 20%;
}

.choose-pay-mode {
  display: flex;
  align-items: center;
}

.ask-for-remarks-view {
  background: white;
  margin-top: 20rpx;
  padding: 0 20rpx;
}

.choose-ask-for {
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 100rpx;
  font-size: 30rpx;
}

.remarks-title-input-num {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.remarks-title {
  font-size: 30rpx;
  line-height: 100rpx;
}

.remarks-input-num {
  font-size: 28rpx;
}

.remarks-view {
  padding-bottom: 20rpx;
}

.textarea-remarks {
  width: 94%;
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 10rpx;
  height: 120rpx;
  font-size: 28rpx;
}

.go-pay-view {
  position: fixed;
  bottom: 0;
  width: 100%;
  line-height: 100rpx;
  display: flex;
  align-items: center;
  background: white;
  box-shadow: -2px 0px 5px 0.5px rgba(0, 0, 0, 0.2);
  font-size: 36rpx;
  z-index: 9;
  border-top: 0.5rpx solid #f5f5f5;
}

.go-pay-money {
  padding: 0 20rpx;
  width: 70%;
  background: white;
  display: flex;
}

.more-pay {
  font-size: 32rpx;
}

.go-pay {
  width: 30%;
  color: white;
  text-align: center;
  font-size: 32rpx;
}

cover-view {
  line-height: 100rpx;
}

/*radio 选项框大小  */

radio .wx-radio-input {
  width: 35rpx;
  height: 35rpx;
  border-radius: 50%;
  border-color: #ededed;
}


.full-reduction-view {
  font-size: 24rpx;
  font-weight: bold;
  padding: 0 10rpx;
  margin: 0 5rpx;
  border-radius: 10rpx;
}

.fullPriceReductionText {
  position: relative;
  left: -47%;
  font-size: 30rpx;
  font-weight: bold;
  top: 0;
}

.fullPriceReductionClass {
  color: gainsboro;
}

.after-discount{
  font-size: 28rpx;
  font-weight: bold;
}

.iconwechat_pay{
  color: #09bb07;
  font-size:40rpx;
}

.iconjifen{
  color: #f0dcab;
  font-size:35rpx;
}

/* 选择商品规格弹窗 */
.weui-show .weui-half-screen-dialog.extClassSpecifications {
  z-index: 9999999;
}

/* 自定义弹窗样式 */
.weui-show .weui-half-screen-dialog.extClassSpecifications {
  padding: 0 20rpx;
}

.weui-half-screen-dialog.extClassSpecifications .weui-half-screen-dialog__ft {
  padding: 20rpx 0 0 0;
  position: sticky;
  bottom: 0;
}

/* 自定义弹出框的最大高度为100%，并设置他的左右上交的border-ric为0 */
.weui-show .weui-half-screen-dialog.extClassSpecifications {
  max-height: 90vh;
  /* border-radius: 0%; */
}

.weui-show .weui-half-screen-dialog.extClassSpecifications .weui-half-screen-dialog__hd {
  padding: 0 20rpx;
}

.good-choice-btn {
  width: 100%;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.radio-group-address{
  display: flex;
  justify-items: center;
  justify-content: center;
  flex-direction: column;
}

.radio-label-address{
  display: flex;
  align-items: center;
}

.choose-address-dialog{
  width: 100%;
  border-bottom: 0.5rpx solid #ededed;
  padding: 20rpx 20rpx 20rpx 0;
}

.address-detail{
  font-size: 28rpx;
}

.address-name-phone{
  font-size: 26rpx;
  color: #7f7f7f;
  margin-right: 20rpx;
}

.footer-btns{

  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1rpx solid #ededed;
  padding-top: 20rpx;
}

.footer-btns view{
  width: 48%;
}

.iconbianji-copy{
  font-size: 50rpx;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.payment-checked{
  background: linear-gradient(to right, #f2e1b5, #ebd198);
}

.payment-not-checked{
  background: #f0f0f0;
}

.radio-label-payment{
  padding: 8rpx 10rpx;
  font-size: 26rpx;
  border-radius: 18rpx;
  margin: 5rpx 0;
}

.pay_radio{
  display: none;
}

.radio-label-payment.flex_center.payment-checked .iconjifen{
  color: black;
}

.actionItem__desc{font-size: 22rpx;color: red;}

.current-tips{
  padding: 20rpx;
  font-size: 38rpx;
  border-radius: 10rpx;
  font-weight: bold;
  color:  red;
}

.top-detail {
  margin: 20rpx;
  background-color: white;
  border-radius: 10rpx;
}

.weui-dialog.extClassIsVip{
  background-color: rgba(255,255,255,0);
}

.weui-dialog.extClassIsVip .weui-dialog__bd{
  padding: 0;
}

.close-view-class .iconfont.icon55{
  font-size: 62rpx;
  color: gainsboro;
}
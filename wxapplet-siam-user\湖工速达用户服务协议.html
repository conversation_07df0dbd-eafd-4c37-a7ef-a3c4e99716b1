<html
  xmlns:o="urn:schemas-microsoft-com:office:office"
  xmlns:w="urn:schemas-microsoft-com:office:word"
  xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882"
  xmlns="http://www.w3.org/TR/REC-html40"
>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="ProgId" content="Word.Document" />
    <meta name="Generator" content="Microsoft Word 14" />
    <meta name="Originator" content="Microsoft Word 14" />
    <link rel="File-List" href="湖工速达用户服务协议.files/filelist.xml" />
    <title></title>
    <style>
      @font-face {
        font-family: "Times New Roman";
      }

      @font-face {
        font-family: "宋体";
      }

      @font-face {
        font-family: "Wingdings";
      }

      @font-face {
        font-family: "<PERSON><PERSON>ri";
      }

      @font-face {
        font-family: "Segoe UI";
      }

      p.MsoNormal {
        mso-style-name: 正文;
        mso-style-parent: "";
        margin: 0pt;
        margin-bottom: 0.0001pt;
        mso-pagination: none;
        text-align: justify;
        text-justify: inter-ideograph;
        font-family: Calibri;
        mso-fareast-font-family: 宋体;
        mso-bidi-font-family: "Times New Roman";
        font-size: 10.5pt;
        mso-font-kerning: 1pt;
      }

      h2 {
        mso-style-name: "标题 2";
        mso-style-noshow: yes;
        mso-style-next: 正文;
        margin-top: 5pt;
        margin-bottom: 5pt;
        mso-margin-top-alt: auto;
        mso-margin-bottom-alt: auto;
        mso-pagination: none;
        text-align: left;
        font-family: 宋体;
        font-weight: bold;
        font-size: 18pt;
      }

      h3 {
        mso-style-name: "标题 3";
        mso-style-noshow: yes;
        mso-style-next: 正文;
        margin-top: 5pt;
        margin-bottom: 5pt;
        mso-margin-top-alt: auto;
        mso-margin-bottom-alt: auto;
        mso-pagination: none;
        text-align: left;
        font-family: 宋体;
        font-weight: bold;
        font-size: 13.5pt;
      }

      span.10 {
        font-family: "Times New Roman";
      }

      p.p {
        mso-style-name: "普通\(网站\)";
        margin: 0pt;
        margin-bottom: 0.0001pt;
        mso-pagination: none;
        text-align: justify;
        text-justify: inter-ideograph;
        font-family: Calibri;
        mso-fareast-font-family: 宋体;
        mso-bidi-font-family: "Times New Roman";
        font-size: 12pt;
        mso-font-kerning: 1pt;
      }

      span.msoIns {
        mso-style-type: export-only;
        mso-style-name: "";
        text-decoration: underline;
        text-underline: single;
        color: blue;
      }

      span.msoDel {
        mso-style-type: export-only;
        mso-style-name: "";
        text-decoration: line-through;
        color: red;
      }

      table.MsoNormalTable {
        mso-style-name: 普通表格;
        mso-style-parent: "";
        mso-style-noshow: yes;
        mso-tstyle-rowband-size: 0;
        mso-tstyle-colband-size: 0;
        mso-padding-alt: 0pt 5.4pt 0pt 5.4pt;
        mso-para-margin: 0pt;
        mso-para-margin-bottom: 0.0001pt;
        mso-pagination: widow-orphan;
        font-family: "Times New Roman";
        font-size: 10pt;
        mso-ansi-language: #0400;
        mso-fareast-language: #0400;
        mso-bidi-language: #0400;
      }
      @page {
        mso-page-border-surround-header: no;
        mso-page-border-surround-footer: no;
      }
      @page Section0 {
        margin-top: 72pt;
        margin-bottom: 72pt;
        margin-left: 90pt;
        margin-right: 90pt;
        size: 595.3pt 841.9pt;
        layout-grid: 15.6pt;
        mso-header-margin: 42.55pt;
        mso-footer-margin: 49.6pt;
      }
      div.Section0 {
        page: Section0;
      }
    </style>
  </head>
  <body style="tab-interval: 21pt; text-justify-trim: punctuation">
    <!--StartFragment-->
    <div class="Section0" style="layout-grid: 15.6pt">
      <h2
        style="
          margin-left: 0pt;
          text-indent: 162.1pt;
          mso-char-indent-count: 9;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            "
            ><font face="Segoe UI">用户服务协议</font></span
          ></b
        ><b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            "
            ><o:p></o:p></span
        ></b>
      </h2>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">最新版本生效日期：</font>202</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">5</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">年</font>7月15日</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI"
            >欢迎您注册易达脉联微信小程序平台账号并使用易达脉联微信小程序平台服务！</font
          ></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI"
            >本《易达脉联微信小程序用户服务协议》为您与易达脉联微信小程序之间就您注册易达脉联微信小程序平台账号并使用易达脉联微信小程序平台各项服务等相关事宜订立的协议。</font
          ></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <h3
        style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            "
            ><font face="Segoe UI">提示条款</font></span
          ></b
        ><b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 13.5pt;
              mso-font-kerning: 0pt;
            "
            ><o:p></o:p></span
        ></b>
      </h3>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI"
            >【审慎阅读】您在申请注册流程中点击同意本协议之前，应当认真阅读本协议。请您务必审慎阅读、充分理解各条款内容，特别是免除或者限制责任的条款、法律适用和争议解决条款。免除或者限制责任的条款将以相依下划线标识，您应重点阅读。如您对协议有任何疑问，可按照本协议中的联系方式向我们咨询，我们会为您做进一步解释和说明。</font
          ></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI"
            >【民事行为能力】您应当具备中华人民共和国法律规定的与您行为相适应的民事行为能力。如您未满</font
          >18周岁或者不具备相应的民事行为能力，请您在监护人的陪同下阅读本服务协议，并在进行注册、下单、支付等任何行为或使用易达脉联微信小程序平台其他任何服务前，应事先征得您法定监护人的同意。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI"
            >【签约动作】当您按照注册页面提示填写信息、阅读并同意本协议且完成全部注册程序后，即表示您已充分阅读、理解并接受本协议的全部内容，并与易达脉联微信小程序达成一致，成为易达脉联微信小程序平台的</font
          ><font face="Segoe UI"
            >“用户”。在阅读本协议的过程中，如果您不同意本协议或其中任何条款约定，您应立即停止注册程序。</font
          ></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><o:p></o:p
        ></span>
      </p>
      <h2
        style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            "
            ><font face="Segoe UI">一、定义</font></span
          ></b
        ><b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            "
            ><o:p></o:p></span
        ></b>
      </h2>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >1.1 易达脉联微信小程序平台：易达脉联微信小程序</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >1.2
          易达脉联微信小程序：您可随时查看平台公示的证照信息以确定向您履约的易达脉联微信小程序主体。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >1.3
          易达脉联微信小程序平台服务：易达脉联微信小程序基于微信小程序向用户提供的各项服务，如用户可以通过系统使用订单管理、商户可以使用此系统进行店铺和外卖信息的发布和订单管理等服务。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">1.4 </font
          ><font face="宋体">特别说明：</font></span
        ><u
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 宋体;
              mso-ascii-font-family: 'Segoe UI';
              mso-hansi-font-family: 'Segoe UI';
              mso-bidi-font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              text-transform: none;
              text-decoration: underline;
              text-underline: single;
              font-style: normal;
              font-size: 12pt;
              mso-font-kerning: 1pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            "
            ><font face="宋体"
              >易达脉联微信小程序平台仅提供信息发布与交易撮合服务，商品</font
            ><font face="Segoe UI">/</font
            ><font face="宋体">服务由入驻商家提供”。</font></span
          ></u
        ><u
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              mso-fareast-font-family: 宋体;
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              text-transform: none;
              text-decoration: underline;
              text-underline: single;
              font-style: normal;
              font-size: 12pt;
              mso-font-kerning: 1pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            "
            ><o:p></o:p></span
        ></u>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >1.</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">5</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >&nbsp;<font face="Segoe UI"
            >易达脉联微信小程序平台规则：包括所有易达脉联微信小程序平台规则频道内或易达脉联微信小程序平台其他相关页面上已经发布及后续发布的全部规则、解读、公告、实施细则等内容。</font
          ></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >1.</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">6</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >&nbsp;<font face="Segoe UI">用户</font
          >/易达脉联微信小程序用户：使用易达脉联微信小程序相关服务的主体，本协议当中有时称“您”。用户需确认，在开始注册程序使用易达脉联微信小程序平台服务前，用户应具备中华人民共和国法律规定的与用户行为相适应的民事行为能力。如用户不具备前述与其行为相适应的民事行为能力，则用户及其监护人应依照法律规定承担相应的法律责任。同时，易达脉联微信小程序发现用户不具备相适应的民事行为能力，有权限制账号使用或采取其他符合法律法规要求的必要措施。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >1.</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">7</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >&nbsp;<font face="Segoe UI"
            >用户注册：是指用户登录易达脉联微信小程序平台，并按要求填写相关信息并确认同意履行相关服务协议、注册易达脉联微信小程序平台账号的过程。</font
          ></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <h2
        style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            "
            ><font face="Segoe UI">二、概述</font></span
          ></b
        ><b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            "
            ><o:p></o:p></span
        ></b>
      </h2>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >2.1
          由于互联网高速发展，您与易达脉联微信小程序签署的本服务协议列明的条款并不能完整罗列并覆盖您与易达脉联微信小程序所有权利与义务，现有的约定也不能保证完全符合未来发展的需求。因此，易达脉联微信小程序平台的隐私权政策（或其更名后的文件）及其他各类易达脉联微信小程序平台规则为本协议的补充协议，与本协议不可分割且具有同等法律效力。如您使用易达脉联微信小程序平台服务，视为您同意上述补充协议。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >2.2
          根据国家法律法规变化、运营需要或为提升服务质量的目的，易达脉联微信小程序将在必要的时候对上述各项协议、条款与规则进行修改更新，并通过在易达脉联微信小程序上发出公告、通知等合理、醒目的方式向您进行提前通知。您应当及时查阅并了解相关更新修改内容，如您不同意相关更新修改内容，可停止使用相关更新修改内容所涉及的服务，此情形下，变更事项对您不产生效力；如您在上述更新修改内容实施后继续使用所涉及的服务，将视为您已同意各项更新修改内容。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <h2
        style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            "
            ><font face="Segoe UI">三、用户的权利和义务</font></span
          ></b
        ><b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            "
            ><o:p></o:p></span
        ></b>
      </h2>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >3.1
          为了给广大用户营造一个健康、有序的网络服务交易平台，易达脉联微信小程序倡导诚信交易，并为此提供一系列的解决方案。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI"
            >易达脉联微信小程序平台为用户交易提供信息服务的平台，易达脉联微信小程序只能部分控制交易所涉及的产品的质量、安全或合法性、商贸信息的真实性或准确性，以及交易方履行其在贸易协议下的各项义务的能力。</font
          ></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <h2
        style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            font-weight: normal;
            text-transform: none;
            font-style: normal;
            font-size: 18pt;
            mso-font-kerning: 0pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >3.2 用户的权利</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            font-weight: normal;
            text-transform: none;
            font-style: normal;
            font-size: 18pt;
            mso-font-kerning: 0pt;
          "
          ><o:p></o:p
        ></span>
      </h2>
      <h3
        style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            font-weight: normal;
            text-transform: none;
            font-style: normal;
            font-size: 13.5pt;
            mso-font-kerning: 0pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >3.2.1
          创建及使用账户：用户有权在向易达脉联微信小程序提交相关注册资料后创立易达脉联微信小程序平台账号，拥有自己在易达脉联微信小程序平台的用户名及密码，并有权利用自己的用户名及密码登录易达脉联微信小程序平台。除因历史原因、业务整合等易达脉联微信小程序所认可的特殊情况外，原则上只允许每位用户使用一个易达脉联微信小程序平台账号。用户不得以任何形式擅自转让或授权他人使用自己的易达脉联微信小程序平台账号。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            font-weight: normal;
            text-transform: none;
            font-style: normal;
            font-size: 13.5pt;
            mso-font-kerning: 0pt;
          "
          ><o:p></o:p
        ></span>
      </h3>
      <h3
        style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            font-weight: normal;
            text-transform: none;
            font-style: normal;
            font-size: 13.5pt;
            mso-font-kerning: 0pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >3.2.2
          浏览信息、确认并提交订单：用户有权利用易达脉联微信小程序平台和易达脉联微信小程序平台商户在线取得直接联系，以获得最新的商品信息和相关增值服务。在浏览相关商品/服务的信息后，用户点击"去结算"按钮后进入订单页面，页面中包括但不限于商品名称、购买数量、应付总额、收货地址（如有）等信息，用户应仔细核对。用户点击订单底部的"提交订单"按钮，视为用户已阅读并同意外卖页面的所有内容并认可订单内容，订单内容及外卖页面信息，为用户与商户、配送服务商之间的外卖交易合同。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            font-weight: normal;
            text-transform: none;
            font-style: normal;
            font-size: 13.5pt;
            mso-font-kerning: 0pt;
          "
          ><o:p></o:p
        ></span>
      </h3>
      <h3
        style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            font-weight: normal;
            text-transform: none;
            font-style: normal;
            font-size: 13.5pt;
            mso-font-kerning: 0pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >3.2.3
          支付价款：上述外卖合同成立后，用户应根据付款页面提示完成外卖价款的支付。在用户支付外卖价款前商家有权不向用户提供外卖商品/服务，易达脉联微信小程序或商家在特定商品或服务页面中特别明示说明的除外。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            font-weight: normal;
            text-transform: none;
            font-style: normal;
            font-size: 13.5pt;
            mso-font-kerning: 0pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><o:p></o:p
        ></span>
      </h3>
      <h2
        style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            "
            >3.2.4 配送服务：</span
          ></b
        ><b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            "
            ><o:p></o:p></span
        ></b>
      </h2>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >(1)
          商户确认用户订单后，选择外卖配送的订单将由商户或第三方配送服务提供商完成配送服务。用户同意并知晓，部分商品由商户向您提供送货服务，在此情况下，送货服务相关标准按该商户在易达脉联微信小程序平台公示的标准执行（包括但不限于费用标准、送货时限等）；同时，部分商品由第三方配送服务商向您提供配送服务，为您提供配送服务的一方将向您收取一定的配送服务费用，具体金额以您下单时系统显示的金额为准，您知悉并授权第三方配送服务商代您向商户取货并将商品送到您所指定的收货地址。用户支付的配送费用由实际的配送服务提供商收取。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >(2)
          用户知悉、理解并同意，基于外卖服务配送服务特性，"预计送达时间"为系统根据配送距离、商品/服务准备时间、用户评价时间等因素进行综合计算得到的参考时间，实际配送时间会受到交通情况、当天天气、订单量、商家实际出餐时间等因素影响而不同。"预计送达时间"不作为易达脉联微信小程序或商家对用户做出的配送时间承诺。用户可根据需要，自行选择购买配送延误增值服务，以便在实际送达时间超过预计送达时间时获得相应补偿。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >(3)
          用户理解并同意，在外卖合同成立且用户付款的情形下，易达脉联微信小程序或商家将协调相应配送资源向用户提供配送服务，配送服务费用（如有）具体金额以用户已确认并提交的订单上显示金额为准。用户知悉并授权易达脉联微信小程序或商家协调相关配送资源（如适用）向商家取货并将商品配送至用户指定收货地址，授权通过易达脉联微信小程序平台向配送服务提供方支付相关配送费用（如有）。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >(4)
          如用户付款成功后，因配送运力不足、不可抗力等导致履约不能的，外卖合同解除，用户有权要求商家进行退款。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >3.2.5
          用户有权以实际支付的金额为限要求发票，商品金额发票由商户提供，配送费金额发票由实际提供配送服务的商户或配送服务商提供。商户及/或配送服务商有权拒绝用户虚开冒开等不合理需求。用户可申请发票的金额为用户订单实际支付的金额；若订单发生退款等情况，用户可申请开票金额为扣除订单退款后用户实际支付的净额。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >3.2.6
          评价和投诉：用户有权对易达脉联微信小程序商户的服务做出评价和投诉，并提出建议和意见。用户的评价行为应当遵守相关法律法规及易达脉联微信小程序平台规则的要求。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >3.2.7
          请求协调纠纷：用户在易达脉联微信小程序平台交易过程中如与商户因交易产生纠纷，可以请求易达脉联微信小程序从中予以协调。对因商户或第三方配送团队原因在整个物流配送过程中导致的货物损坏、损毁、灭失等情况，将由对应责任方承担赔偿责任，易达脉联微信小程序将根据平台规则，积极配合用户对该部分损失进行追偿。用户如发现其他用户有违法或违反本服务协议的行为，可以向易达脉联微信小程序进行反映要求处理。如用户因交易与其他用户产生诉讼的，用户有权通过司法部门要求易达脉联微信小程序提供相关资料。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <h2
        style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            "
            >3.3 用户的义务</span
          ></b
        ><b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            "
            ><o:p></o:p></span
        ></b>
      </h2>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >3.3.1
          用户有义务在注册及使用易达脉联微信小程序平台时提供自己的真实资料，并保证诸如电子邮件地址、联系电话、联系地址等内容的有效性及安全性，保证易达脉联微信小程序及其他用户可以通过上述联系方式与您进行联系。同时，用户也有义务在相关资料实际变更时及时更新有关资料。用户保证不以他人资料在易达脉联微信小程序平台进行注册。若用户使用虚假电话、姓名、地址或冒用他人信息使用易达脉联微信小程序平台服务，易达脉联微信小程序有权屏蔽地址或做出其他的处理。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >3.3.2
          用户因进行交易、获取订购商品的有偿服务或接触易达脉联微信小程序平台服务器而发生的所有应纳税赋，以及一切硬件、软件、服务及其他方面的费用均由用户负责支付。易达脉联微信小程序平台仅作为网络交易信息服务平台。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >3.3.3
          为确保评价内容能够为消费者购物决策提供可靠的依据，反映商品及服务的真实情况，用户的评价应当客观、真实，与交易的商品及/或服务具有关联性。用户不应该在易达脉联微信小程序平台恶意评价其他商户、采取不正当手段提高自身的信用度或降低其他商户的信用度，评价信息不应该包含任何污言秽语、色情低俗、广告信息或法律法规与本协议、《易达脉联微信小程序用户评价规则》等易达脉联微信小程序平台规则列明的其他禁止性信息。如用户评价违反法律法规、社会公共利益及《易达脉联微信小程序用户评价规则》等易达脉联微信小程序平台相关规则，易达脉联微信小程序有权对用户实施上述行为所产生的评价信息采取必要处置措施，包括但不限于删除相关评价内容。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >3.3.4
          用户承诺在使用易达脉联微信小程序服务过程中，遵守法律法规及易达脉联微信小程序平台规则，以诚实信用为基础使用易达脉联微信小程序服务，不会出现违反诚实信用原则、法律法规及易达脉联微信小程序平台规则的情形，该等违规情形包括但不限于：</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">1</font><font face="宋体">、</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">批量注册易达脉联微信小程序账号；</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">2</font><font face="宋体">、</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI"
            >虚假交易、制造虚假订单、虚假分享、恶意评论；</font
          ></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">3</font><font face="宋体">、</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">滥用用户权利、侵犯他人合法权益；</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">4</font><font face="宋体">、</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI"
            >无正当理由批量购买后批量申请退款、利用系统漏洞获取利润、违规套现等影响正常易达脉联微信小程序平台服务提供或妨碍平台运营秩序的行为；</font
          ></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">5</font><font face="宋体">、</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI"
            >其他违反诚实信用原则，或损害易达脉联微信小程序平台、商户、其他用户或其他第三方合法权益的行为；</font
          ></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">6</font><font face="宋体">、</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI"
            >其他违反国家法律法规及易达脉联微信小程序平台规定要求，或损害各种社会公共利益或公共道德的行为。</font
          ></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI"
            >如出现以上违规行为，用户需承担相应法律责任；如因用户该等违规行为给易达脉联微信小程序、商家或其他第三方造成损失的，用户应当承担赔偿责任。此外，根据具体违法违规情形，易达脉联微信小程序有权作出独立判断，立即暂停或终止对您提供部分或全部服务，包括锁定、注销、删除帐号等措施，或者根据法律法规或者平台规则采取其他措施。</font
          ></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >3.3.5
          用户同意，不得对易达脉联微信小程序平台上任何资料作商业性利用，包括但不限于在未经易达脉联微信小程序事先书面同意的情况下，以复制、传播等方式使用在易达脉联微信小程序平台上展示的任何资料。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >3.3.6
          用户同意接收来自易达脉联微信小程序或者易达脉联微信小程序平台合作伙伴发出的邮件、信息。如果您不愿意接受此类信息，您有权通过易达脉联微信小程序平台提供的相应退订方式进行退订。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <h2
        style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            "
            ><font face="Segoe UI"
              >四、易达脉联微信小程序的权利和义务</font
            ></span
          ></b
        ><b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            "
            ><o:p></o:p></span
        ></b>
      </h2>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >4.1 易达脉联微信小程序的权利</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >4.1.1
          用户在易达脉联微信小程序平台交易过程中如与商户、其他用户或其他第三方因交易产生纠纷，请求易达脉联微信小程序从中予以调处，经易达脉联微信小程序审核后，易达脉联微信小程序有权通过电话、电子邮件、站内信或其他合理方式向纠纷双方了解情况，并将所了解的情况通过电话、电子邮件、站内信或其他合理方式通知对方。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >4.1.2
          易达脉联微信小程序有权对用户的注册资料及交易行为进行查阅，发现注册资料或交易行为中存在违反国家法律法规或易达脉联微信小程序平台规则时，易达脉联微信小程序有权根据相关规定进行处理。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >4.1.3
          许可使用权。对于用户提供、发布及在使用易达脉联微信小程序平台服务中形成的除个人信息外的文字、图片、视频、音频等非个人信息，在法律规定的保护期限内用户免费授予易达脉联微信小程序及其关联公司获得全球排他的许可使用权和及再授权给其他第三方使用并可以自身名义对第三方侵权行为取证及提起诉讼的权利。您同意易达脉联微信小程序及其关联公司存储、使用、复制、修订、编辑、发布、展示、翻译、分发您的非个人信息或制作其派生作品，并以已知或日后开发的形式、媒体或技术将上述信息纳入其它作品内。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <h2
        style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            "
            >4.2 易达脉联微信小程序的义务</span
          ></b
        ><b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            "
            ><o:p></o:p></span
        ></b>
      </h2>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >4.2.1
          易达脉联微信小程序有义务在现有技术上维护整个交易平台的正常运行，并努力提升和改进技术，使用户网上订购交易活动得以顺利进行。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >4.2.2
          对用户在注册使用易达脉联微信小程序平台中所遇到的与交易或注册有关的问题及反映的情况，易达脉联微信小程序应及时做出回复。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >4.2.3
          用户因在易达脉联微信小程序平台进行的订购交易与其他用户产生诉讼的，用户通过司法部门或行政部门依照法定程序要求易达脉联微信小程序提供相关资料，易达脉联微信小程序应积极配合并提供有关资料。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <h2
        style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            "
            ><font face="Segoe UI">五、隐私权政策</font></span
          ></b
        ><b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            "
            ><o:p></o:p></span
        ></b>
      </h2>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >5.1
          易达脉联微信小程序非常重视用户个人信息的保护，根据有关法律法规要求，网络产品、服务具有收集用户信息功能的，其提供者应当向用户明示并取得同意。易达脉联微信小程序特此通过单独明示的《隐私权政策》明确向您告知收集、使用用户个人信息的目的、方式和范围，查询、更正信息的渠道以及拒绝提供信息的后果。易达脉联微信小程序希望通过隐私权政策向您清楚地介绍易达脉联微信小程序对您个人信息的处理方式，因此易达脉联微信小程序建议您完整地阅读《隐私权政策》，以帮助您更好地保护您的隐私权。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <h2
        style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            "
            ><font face="Segoe UI">六、用户违约及处理</font></span
          ></b
        ><b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            "
            ><o:p></o:p></span
        ></b>
      </h2>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >6.1 发生如下情形之一的，视为您违约：</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >6.1.1 使用我方平台服务时违反有关法律法规规定的；</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >6.1.2 违反本协议或本协议补充协议约定的。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI"
            >为适应互联网行业发展和满足海量用户对高效优质服务的需求，您理解并同意，易达脉联微信小程序可在易达脉联微信小程序平台规则中约定违约认定的程序和标准。如：易达脉联微信小程序可依据您的用户数据与海量用户数据的关系来认定您是否构成违约；您有权和对您的数据异常现象进行充分举证和管理解释，否则将被认定为违约。</font
          ></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >6.2 违约处理措施</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >6.2.1
          您在易达脉联微信小程序平台上发布的内容和信息构成违约的，易达脉联微信小程序有权根据相应规则立即对相应内容和信息进行删除、屏蔽等处理或对您的账号进行暂停使用、查封、注销等处理。易达脉联微信小程序平台根据法律法规的明确规定承担相应的责任。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >6.2.2
          您在易达脉联微信小程序平台上实施的行为，或虽未在易达脉联微信小程序平台上实施但对易达脉联微信小程序平台及其用户产生影响的行为构成违约的，易达脉联微信小程序可依据相应规则对您的账号执行限制参加活动、中止向您提供部分或全部服务等处理措施。如您的行为构成根本违约的，易达脉联微信小程序可查封您的账号，终止向您提供服务。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >6.2.3
          如果您在易达脉联微信小程序平台上的行为违反相关的法律法规，易达脉联微信小程序可依法向相关主管机关报告并提交您的使用记录和其他信息。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >6.3
          如您的行为使易达脉联微信小程序及/或其关联公司遭受损失（包括自身的直接经济损失、商誉损失及对外支付的赔偿金、和解款、律师费、诉讼费等间接经济损失），您应赔偿易达脉联微信小程序及/或其关联公司的上述全部损失。如您的行为使易达脉联微信小程序及/或其关联公司遭受第三人主张权利，易达脉联微信小程序及/或其关联公司可在对第三人承担金钱给付等义务后就全部损失向您追偿。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <h2
        style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            "
            ><font face="Segoe UI">七、协议的终止</font></span
          ></b
        ><b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            "
            ><o:p></o:p></span
        ></b>
      </h2>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >7.1 用户有权通过以下任一方式终止本协议：</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >7.1.1
          在满足易达脉联微信小程序平台公示的账号注销条件时您通过易达脉联微信小程序平台注销您的账号的；</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >7.1.2 变更事项生效前您停止使用并明示不愿接受变更事项的；</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >7.1.3
          您明示不愿继续使用易达脉联微信小程序平台服务，且符合易达脉联微信小程序平台终止条件的。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >7.2 出现以下情况时，易达脉联微信小程序可以通过您终止本协议：</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >7.2.1
          您违反本协议约定，易达脉联微信小程序依据违约条款终止本协议的；</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >7.2.2
          您盗用他人账号、发布违禁信息、骗取他人财物、扰乱市场秩序、采取不正当手段谋利等行为，易达脉联微信小程序依据易达脉联微信小程序平台规则对您的账号予以查封的；</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >7.2.3
          除上述情形外，因您多次违反易达脉联微信小程序平台规则相关规定且情节严重，易达脉联微信小程序依据易达脉联微信小程序平台规则对您的账号予以查封的；</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >7.2.4 您的账号被易达脉联微信小程序依据本协议进行注销等清理的；</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >7.2.5
          您在易达脉联微信小程序平台有侵犯他人合法权益或其他严重违法违约行为的；</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >7.2.6
          其它根据相关法律法规易达脉联微信小程序应当终止服务的情况。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >7.3
          本协议终止后，除法律法规另有规定外，原则上易达脉联微信小程序无义务向您或您指定的第三方披露您账号中的任何信息。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >7.4 本协议终止后，易达脉联微信小程序仍享有下列权利：</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >7.4.1
          易达脉联微信小程序可根据适用法律的要求删除您的个人信息，或使其匿名化处理，也可依照法律规定由期限和方式继续保存您留存于易达脉联微信小程序平台的其他内容和信息。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >7.4.2
          对于您过往的违约行为，易达脉联微信小程序仍可依据本协议向您追究违约责任。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <h2
        style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            "
            ><font face="Segoe UI">八、责任范围</font></span
          ></b
        ><b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            "
            ><o:p></o:p></span
        ></b>
      </h2>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI"
            >易达脉联微信小程序依照法律规定履行基础保障义务，但对于下述原因导致的协议履行障碍、履行瑕疵、履行延后或履行内容变更等情形，易达脉联微信小程序并不承担相应的违约责任：</font
          ></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">1</font><font face="宋体">、</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI"
            >因自然灾害、罢工、暴乱、战争、政府行为、司法行政命令等不可抗力因素；</font
          ></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 0pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-pagination: widow-orphan;
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">2</font><font face="宋体">、</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI"
            >在易达脉联微信小程序已尽善意管理的情况下，因常规或紧急的设备与系统维护、设备与系统故障、网络信息与数据安全等因素。</font
          ></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <h2
        style="
          margin-left: 0pt;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          mso-line-height-alt: 12pt;
          background: rgb(255, 255, 255);
        "
      >
        <b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
              background: rgb(255, 255, 255);
              mso-shading: rgb(255, 255, 255);
            "
            ><font face="Segoe UI">九、附则</font></span
          ></b
        ><b
          ><span
            style="
              mso-spacerun: 'yes';
              font-family: 'Segoe UI';
              color: rgb(64, 64, 64);
              letter-spacing: 0pt;
              font-weight: bold;
              text-transform: none;
              font-style: normal;
              font-size: 18pt;
              mso-font-kerning: 0pt;
            "
            ><o:p></o:p></span
        ></b>
      </h2>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >9.1
          本协议之订立、生效、解释、修订、补充、终止、执行与争议解决均适用中华人民共和国法律；如法律无相关规定的，参照商业惯例及/或行业惯例。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >9.2
          您因使用易达脉联微信小程序平台服务所产生及与易达脉联微信小程序平台服务有关的争议，由易达脉联微信小程序与您协商解决。协商不成时，任何一方均可向被告所在地人民法院提起诉讼。本协议任一条款被视为废止、无效或不可执行，该条应视为可分的且并不影响本协议其余条款的有效性及可执行性。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 5pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          mso-margin-bottom-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          >9.3
          如您对本服务协议有任何问题或建议，可通过易达脉联微信小程序客服与我们取得联系。</span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          margin-left: 0pt;
          mso-margin-top-alt: auto;
          text-indent: 0pt;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><o:p>&nbsp;</o:p></span
        >
      </p>
      <p
        class="p"
        style="
          margin-top: 5pt;
          margin-right: 0pt;
          margin-bottom: 0pt;
          mso-margin-top-alt: auto;
          text-indent: 192pt;
          mso-char-indent-count: 16;
          mso-pagination: widow-orphan;
          background: rgb(255, 255, 255);
        "
      >
        <span
          style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="宋体">湖南</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">易达脉联</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 宋体;
            mso-ascii-font-family: 'Segoe UI';
            mso-hansi-font-family: 'Segoe UI';
            mso-bidi-font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="宋体">电子商务</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
            background: rgb(255, 255, 255);
            mso-shading: rgb(255, 255, 255);
          "
          ><font face="Segoe UI">有限公司</font></span
        ><span
          style="
            mso-spacerun: 'yes';
            font-family: 'Segoe UI';
            color: rgb(64, 64, 64);
            letter-spacing: 0pt;
            text-transform: none;
            font-style: normal;
            font-size: 12pt;
            mso-font-kerning: 1pt;
          "
          ><o:p></o:p
        ></span>
      </p>
      <p class="MsoNormal">
        <span
          style="
            mso-spacerun: 'yes';
            font-family: Calibri;
            mso-fareast-font-family: 宋体;
            mso-bidi-font-family: 'Times New Roman';
            font-size: 10.5pt;
            mso-font-kerning: 1pt;
          "
          ><o:p>&nbsp;</o:p></span
        >
      </p>
    </div>
    <!--EndFragment-->
  </body>
</html>

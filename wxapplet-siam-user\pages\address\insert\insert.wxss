page{
  background: #f5f5f5;
}

input{
  font-size: 30rpx;
  width: 80%;
}

.address-info-list{
  margin-top: 20rpx;
  padding: 0 20rpx;
  background: white;
}

.address-info-item{
  display: flex;
  align-items: center;
  line-height: 100rpx;
  border-bottom: 0.5rpx solid #f5f5f5;
}

.address-info-item text{
  width: 20%;
  font-size: 28rpx;
}

radio .wx-radio-input {
  width: 35rpx;
  height: 35rpx;
  border-radius: 50%;
  border-color: #ededed;
}

.sex-radio-group{
  width: 50%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sex-radio-group-label{
  font-size: 30rpx;
  border-radius: 50rpx;
}

.tag-radio-group{
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 10rpx;
  border-radius: 50rpx;
}

.tag-radio-group-label{
  width: 15%;
  height: 40rpx;
  margin-right: 20rpx;
  color: #808080;
  border-radius: 30rpx;
  font-size: 26rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tag-unchecked{
  background: #f5f5f5;
}

.tag-active{
  color: white;
}

.picker{
  font-size: 28rpx;
}

.operation-view{
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  background: white;
  position: fixed;
  bottom: 0;
  font-size: 30rpx;
  display: flex;
}

.delete-view{
  width: 50%;
  text-align: center;
}

button::after{
  border: none;
}

.save-view{
  width: 100%;
  text-align: center;
  color: white;
  border-radius: 0;
  border:none;
  font-size: 30rpx;
  line-height: 100rpx;
}

.setting-default{
  height: 60rpx;
  line-height: 70rpx;
  margin-top: 20rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
}

checkbox{
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border-color: #ededed;
  display: flex;
  align-items: center;
}

checkbox .wx-checkbox-input{
  width: 40rpx;
  height: 40rpx;
}

.default-words{
  margin-left: 30rpx;
}

.section{
  width: 80%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section input{
  width: 90%;
}
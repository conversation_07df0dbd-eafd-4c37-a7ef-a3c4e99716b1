page {
  background: #f5f5f5;
}

.section-location{
  position: sticky;
  top: 0;
}

.section {
  background: white;
  padding: 10rpx 20rpx;
  display: flex;
  align-items: center;
  border-bottom: 0.5rpx solid #c3c3c3;
  
}

.section input {
  width: 80%;
  margin: 5rpx 10rpx;
  border: 1px solid #c3c3c3;
  height: 30px;
  border-radius: 3px;
  padding: 0 5px;
}

.text_box {
  margin: 25rpx 20rpx;
  padding: 22rpx 20rpx;
  background: white;
  border-radius: 15rpx;
}

picker {
  width: 30%;
}

.picker {
  font-size: 28rpx;
}

.address-name {
  font-size: 28rpx;
  font-weight: bold;
}

.address-address {
  font-size: 24rpx;
  color: #c3c3c3;
}

.address-title{
  padding: 20rpx 20rpx 10rpx 20rpx;
  background: #f5f5f5;
  color: #7e7e7e;
  font-size: 30rpx;
}

.location-name{
  background: white;
  padding: 10rpx 20rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.please-location{
 color: #7e7e7e;
}

.delivery-address{
  padding: 0 20rpx;
  background: white;
}

.delivery-address-item{
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.delivery-address-detail{
  font-size: 28rpx;
  font-weight: bold;
}

.delivery-address-realname-phone{
  color: #7e7e7e;
  font-size: 24rpx;
}
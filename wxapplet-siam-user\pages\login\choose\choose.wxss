.userinfo-view{
  text-align: center;
  margin: 50rpx 0;
}

.userinfo-avatar {
  width: 108rpx;
  height: 108rpx;
  border-radius: 50%;
  box-shadow: -2px 0px 5px 0.5px rgba(0, 0, 0, 0),0px -2px 5px 1px rgba(0, 0, 0, 0.1),2px 0px 5px 1px rgba(0, 0, 0, 0),0px 2px 5px 1px rgba(0, 0, 0, 0.1)
}

.userinfo-text{
  line-height: 58rpx;
  font-size: 32rpx;
}

.button-view{
  text-align: center;
}

.getphonenumber{
  margin: 150rpx 50rpx 50rpx 50rpx;
  font-size: 32rpx;
}

.verification-code{
  margin: 50rpx 50rpx 50rpx 50rpx;
  background: white;
  font-size: 32rpx;
}

/* 协议相关样式 */
.agreement-section {
  padding: 30rpx 50rpx;
  text-align: center;
}

.agreement-checkbox {
  display: flex;
  justify-content: center;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
}

.agreement-text {
  font-size: 24rpx;
  color: #666;
  margin: 0 5rpx;
}

.agreement-link {
  font-size: 24rpx;
  color: #004ca0;
  text-decoration: underline;
  margin: 0 5rpx;
}

/* 禁用状态的按钮样式 */
.getphonenumber[disabled] {
  background-color: #f0f0f0 !important;
  color: #ccc !important;
}

.verification-code[disabled] {
  background-color: #f0f0f0 !important;
  color: #ccc !important;
  border-color: #e0e0e0 !important;
}

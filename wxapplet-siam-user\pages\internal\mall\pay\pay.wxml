<view class="top-detail" wx:if="{{tipReward}}">
	<view class="current-tips" catchtap="customerServiceWechat">
		{{tipReward}}
	</view>
</view>
<view class="ask-for-delivery-detail margin-border-radius">
  <view class="ask-for-delivery-title">收货地址</view>
  <view class="ask-for-delivery" bindtap="openAddressDialog">
    <view class="ask-for-delivery-house-view" wx:if="{{deliveryAndSelfTaking.deliveryAddress}}" data-radioIndex="0">
      <view class="ask-for-delivery-view">
        <view class="ask-for-delivery-house">
          <text class="iconfont icondizhi"></text>
          <view class="right-class out_of_range two_row">
            {{deliveryAndSelfTaking.deliveryAddress.province}}{{deliveryAndSelfTaking.deliveryAddress.city}}
            {{deliveryAndSelfTaking.deliveryAddress.area}}{{deliveryAndSelfTaking.deliveryAddress.street}}
          </view>
        </view>
        <view class="ask-for-delivery-address-detail">
          <text class="iconfont icon002dianhua"></text>
          <view class="right-class">
            <text>{{deliveryAndSelfTaking.deliveryAddress.phone}}</text>
            <text
              decode="true">&nbsp;&nbsp;&nbsp;{{deliveryAndSelfTaking.deliveryAddress.realname}}{{deliveryAndSelfTaking.deliveryAddress.sex==0?"先生":"女士"}}</text>
          </view>
        </view>
      </view>
      <text class="iconfont iconhtbArrowright02"></text>
    </view>
    <view class="please-address" wx:else data-radioIndex="0">
      <text>请选择地址</text><text class="iconfont iconhtbArrowright02"></text>
    </view>
  </view>
</view>
<view class="shopping-commodity-details margin-border-radius">
  <view class="commodity-name-price-detail" wx:for="{{data.orderDetailList}}" wx:key="index">
    <view class="commodity-name-types">
      <view class="commodity-name">{{item.goodsName}}</view>
      <view class="commodity-types">{{item.restructure}}</view>
    </view>
    <view class="commodity-totalnum-price">
      <view class="commodity-totalnum">x{{item.number}}</view>
      <view class="commodity-price">￥{{item.price}}</view>
    </view>
  </view>
  <view class="commodity-name-price-detail">
    <view class="commodity-name-types">
      <view class="commodity-name">运费</view>

    </view>
    <view class="commodity-totalnum-price">
      <view></view>
      <view class="commodity-types commodity-delivery-tip theme-color">免运费</view>
    </view>
  </view>
  <view class="commodity-name-price-detail" wx:if="{{radioIndex==1}}">
    <view class="commodity-name-types">
      <view class="commodity-name">配送费</view>
      <!-- <view class="commodity-types commodity-delivery-tip theme-color">面价满55元免配送费</view> -->
    </view>
    <view class="commodity-totalnum-price">
      <view></view>
      <view class="commodity-price">￥{{feeData}}</view>
    </view>
  </view>
  <view class="commodity-name-price-detail" wx:if="{{data.fullPriceReductionIsHidden}}">
    <view class="commodity-name-types">
      <view class="commodity-name">满减</view>
      <!-- <view class="commodity-types commodity-delivery-tip theme-color">面价满55元免配送费</view> -->
    </view>
    <view class="commodity-totalnum-price">
      <view></view>
      <view class="commodity-price full-reduction-view theme-color-border">{{data.fullReductionRuleName}}</view>
    </view>
  </view>
  <view class="view-line"></view>
  <view class="total-money-view">
    <text class="money-icon commodity-price">总计：</text>
    <view class="{{data.fullPriceReductionIsHidden||data.couponsIsHidden?'fullPriceReductionClass':''}}">
      <text
        class="commodity-price {{data.fullPriceReductionIsHidden||data.couponsIsHidden?'strike_through':''}}">￥{{data.actualPrice}}</text>
    </view>
    <text class="commodity-price"
      wx:if="{{data.fullPriceReductionIsHidden||data.couponsIsHidden}}">￥{{data.fullPriceReduction}}</text>
  </view>
</view>
<view class="pay-mode-view margin-border-radius">
  <view class="pay-mode-title">优惠券</view>
  <view class="choose-pay-mode" bindtap="{{afterDiscount?'onCoupon':''}}">
    <view class="theme-color after-discount flex_end" wx:if="{{afterDiscount}}">
      <view class="flex_end">
        <text class="coupons-name-text"
          decode="true">{{afterDiscount.couponsName?afterDiscount.couponsName:availableCouponSize>0?"未使用优惠券&nbsp;&nbsp;可用"+availableCouponSize+"张":''}}</text>
        <text decode="true">{{afterDiscount.couponsName?'&nbsp;&nbsp;优惠'+afterDiscount.price+'元':""}}</text>
      </view>
    </view>
    <view class="theme-color after-discount" wx:else>
      <text class="out_of_range one_row">无可用优惠券</text>
    </view>
    <text class="iconfont iconhtbArrowright02"></text>
  </view>
</view>
<!-- <view class="pay-mode-view margin-border-radius">
  <view>支付方式</view>
  <view class="choose-pay-mode" bindtap="choosePayModeTap">
    <text class="iconfont {{paymentModes[paymentModeIndex].icon}}"></text>
    <text decode="true">&nbsp;{{paymentModes[paymentModeIndex].text}}</text>
    <text class="iconfont iconhtbArrowright02"></text>
  </view>
</view> -->
<view class="pay-mode-view margin-border-radius">
  <view>支付方式</view> 
  <view class="choose-pay-mode">
    <radio-group class="radio-group-address" bindchange="radioChangePayment" data-firstIndex="{{key}}">
      <label wx:for="{{paymentModes}}" wx:key="index" wx:if="{{item.show}}"
      class="radio-label-payment flex_center {{item.checked?'payment-checked':'payment-not-checked'}}">
        <radio value="{{index}}" checked="{{item.checked}}" class="pay_radio" />
        <text class="iconfont {{item.icon}}"></text>
        <text decode="true">&nbsp;{{item.text}}</text>
        <text decode="true" class="actionItem__desc" wx:if="{{item.desc}}">&nbsp;{{item.desc}}</text>
      </label>
    </radio-group>
  </view>
</view>
<view class="ask-for-remarks-view margin-border-radius">
  <view class="view-line" wx:if="{{radioIndex==0}}"></view>
  <view class="remarks-view">
    <view class="remarks-title-input-num">
      <view class="remarks-title">特殊备注要求</view>
      <view class="remarks-input-num">{{inputLength}}/30</view>
    </view>
    <textarea class="textarea-remarks" bindinput="remarksInput" maxlength="30" placeholder="输入其他特殊备注要求（可不填）"
      placeholder-class="textarea-placeholder"></textarea>
  </view>
</view>
<view class="go-pay-view">
  <view class="go-pay-money">
    <view class="more-pay">
      <view>还需支付</view>
    </view>
    <view class="total-money" wx:if="{{!data.fullPriceReductionIsHidden&&!data.couponsIsHidden}}">
      ￥{{data.actualPrice}}</view>
    <view class="total-money" wx:else>￥{{data.fullPriceReduction}}</view>

  </view>
  <view class="go-pay theme-bg" hover-class="hover-class-public" bindtap="weChatPayTap">
    去支付
  </view>
</view>
<mp-actionSheet bindactiontap="goToPay" show="{{dialogShow}}" actions="{{paymentModes}}" title="{{title}}">
</mp-actionSheet>
<mp-halfScreenDialog show="{{addressDialog}}" extClass="extClassSpecifications">
  <view slot="title">选择地址</view>
  <view slot="desc">
    <scroll-view scroll-y style="height: 66vh;">
      <radio-group class="radio-group-address" bindchange="radioChangeAddress" data-firstIndex="{{key}}">
        <label wx:for="{{addressList}}" wx:key="index" class="radio-label-address">
          <radio value="{{index}}" checked="{{item.id==deliveryAndSelfTaking.deliveryAddress.id}}" class="radio" />
          <view class="choose-address-dialog">
            <view class="address-detail">{{item.province}}{{item.city}}{{item.area}}{{item.street}}</view>
            <view>
              <text class="address-name-phone">{{item.realname}}</text>
              <text class="address-name-phone">{{item.phone}}</text>
            </view>
          </view>
          <text class="iconfont iconbianji-copy" catchtap="editAddress" data-data="{{item}}"
            data-key="{{index}}"></text>
        </label>
      </radio-group>
    </scroll-view>
    <view slot="footer" class="footer-btns">
      <view class="good-choice-btn theme-color-border" bindtap="insertAddress">新增</view>
      <view class="good-choice-btn theme-bg" bindtap="confirmChooseAddress">确认选择</view>
    </view>
  </view>
</mp-halfScreenDialog>
<mp-halfScreenDialog show="{{showPayPwdInput}}" extClass="extClassPayPwd" bind:bindtouchend="bindtouchend"
  bind:close="balancePayFail" maskClosable="{{maskClosable}}">
  <view slot="title">输入支付密码</view>
  <view slot="desc">
    <view class='password_dialog_tip'><text>使用会员卡余额支付需要验证身份，验证通过后才可进行支付。</text></view>
    <view class='password_dialog_row' catchtap='getFocus'>
      <view class='password_dialog_item_input' wx:for='{{6}}' wx:key='item' wx:for-index='i'>
        <text wx:if='{{pwdVal.length>i}}'></text>
      </view>
    </view>
    <view class='theme-color password_dialog_forget_pwd' catchtap='forgetThePassword'>忘记密码</view>
    <input class='password_dialog_input_control' password type='number' focus='{{payFocus}}'
      hold-keyboard="{{holdKeyboard}}" value="{{pwdVal}}" bindinput='inputPwd' maxlength='6'
      adjust-position="{{adjustPosition}}" cursor-spacing="100" />
  </view>
  <view slot="footer">
  </view>
</mp-halfScreenDialog>
<mp-dialog show="{{isVipDialogShow}}" buttons="{{buttons}}" mask-closable="{{maskClosable}}" catchtouchmove="true" extClass="extClassIsVip"
ext-class="{{extClass}}" bind:tap="close">
	<image src="https://shop-beingwell.oss-cn-hangzhou.aliyuncs.com/data/images/business/vip_recharge_guide.png?v={{timestamp}}" mode="widthFix" class="now-order-image" bindtap="goToRecharge"></image>
</mp-dialog>

page{
  background: #f5f5f5;
  padding-bottom: 20rpx;
}

.status-to-greet{
  background: white;
  text-align: center;
  padding: 20rpx;
  display: flex;
  align-items: center;
  flex-direction: column;
  margin: 20rpx;
  border-radius: 15rpx;
}

.status-view{
  font-size: 40rpx;
  line-height: 88rpx;
  font-weight: bold;
}

.caozuo-buttons{
  display: flex;
  align-items: center;
  /* justify-content: center; */
  width: 100%;
  margin: 20rpx;
  flex-wrap: wrap;
}

.caozuo-buttons .cancel-button{
  width: 199rpx;
  margin: 0;
  margin-right:10rpx;
}

.quxiao-shenqing-button{
  border: 1rpx #898989 solid;

}

.pinglun-btn{
  margin: 0;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

.appraise-button-view{
  text-align: center;
  background: white;
}

.unpaid-class{
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}

.comfirm-tip{
  font-size: 24rpx;
}

.pay-cancel-button{
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 60rpx;
  font-size: 26rpx;
  width: 100%;
  margin: 30rpx;
}

.cancel-button,.pay-button{
  width: 45%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 15rpx;
}

.cancel-button{
  border:1rpx #898989 solid;
  margin: 20rpx;
  border-radius: 15rpx;
}

.refund-process-view{
  margin: 20rpx;
  border-radius: 15rpx;
  background-color: white;
  padding: 20rpx;
}

.refund-process-detail{
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 30rpx;
}

.refund-process-right{
  display: flex;
  align-items: center;
}

.wenxin-tip{
  color: #979797;
  font-size: 26rpx;
}

.right-gray {
  width: 20rpx;
  height: auto;
  margin-left: 20rpx;
}


.to-greet-view{
  font-size:38rpx;
}

.order-queueNo{
  font-size: 50rpx;
  font-weight: bold;
}

.order-details-view{
  margin: 20rpx;
  border-radius: 15rpx;
  background: white;
  padding: 0 20rpx;
}

.order-number-time{
  line-height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
}

.order-number-text{
  font-size: 24rpx;
}

.time-text{
  color: #898989;
  font-size: 24rpx;
}

.commodity-details-view{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
  margin-bottom: 10rpx;
}

.commodity-name-specs{
  width: 75%;
  padding-right: 20rpx;
}

.commodity-name{
  font-size: 30rpx;
  line-height: 58rpx;
  font-weight: bold;
}

.commodity-specs{
  font-size: 24rpx;
}

.commodity-number-money{
  width: 25%;
  display: flex;
  justify-content: space-between;
}

.commodity-number{
  font-size: 28rpx;
}

.commodity-money{
  font-size: 30rpx;
  font-weight: bold;
}

.distribution-fee-view{
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* line-height: 88rpx; */
  padding: 10rpx 0;
  font-size: 28rpx;
}

.actual-payment-view{
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 88rpx;
}

.actual-payment-number{
  font-size: 26rpx;
}

.actual-payment-title{
  font-size: 32rpx;
  margin-right: 10rpx;
}

.actual-payment-money{
  font-size: 32rpx;
  font-weight: bold;
}

.order-time-view{
  margin: 20rpx;
  border-radius: 15rpx;
  background: white;
  color: #898989;
  line-height:78rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.receiving-address-view{
  margin: 20rpx;
  border-radius: 15rpx;
  background: white;
  display: flex;
  justify-content: space-between;
  padding: 40rpx 20rpx;
}

.receiving-address-title{
  width: 30%;
  font-size: 28rpx;
  color: #898989;
}

.receiving-address-details{
  width: 80%;
  font-size: 28rpx;
}

.receiving-address{
  text-align: end;
}

.username-phone{
  text-align: end;
}

.fullPriceReductionText {
  position: relative;
  left: -47%;
  font-size: 30rpx;
  font-weight: bold;
  top: 0;
}

.fullPriceReductionClass {
  color: gainsboro;
  display: flex;
  align-items: center;
}

.choiceReason-radio-group{
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.choiceReason-lable{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  font-size: 30rpx;
}

.choiceReason-border{
  border-bottom: 1rpx solid #f5f5f5;
}

.good-choice-btn {
  width: 100%;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
}

/* 自定义弹出框的最大高度为100%，并设置他的左右上交的border-ric为0 */
.weui-show .weui-half-screen-dialog.extClassShoppingCart {
  max-height: 100vh;
  padding: 0 20rpx;
  position: fixed;
  bottom: 0;
  z-index: 99999;
  /* padding-bottom: 12%; */
}

.weui-half-screen-dialog.extClassShoppingCart .weui-half-screen-dialog__ft {
  padding: 20rpx 0;
  position: sticky;
  bottom: 0;
}


.weui-show .weui-half-screen-dialog.extClassShoppingCart .weui-half-screen-dialog__hd {
  padding: 0 20rpx;
}

/* 选择商品规格弹窗 */
.weui-show .weui-half-screen-dialog.extClassSpecifications {
  z-index: 9999999;
}

/* 自定义弹窗样式 */
.weui-show .weui-half-screen-dialog.extClassSpecifications {
  padding: 0 20rpx;
}

.weui-half-screen-dialog.extClassSpecifications .weui-half-screen-dialog__ft {
  padding: 20rpx 0 0 0;
  position: sticky;
  bottom: 0;
}

/* 自定义弹出框的最大高度为100%，并设置他的左右上交的border-ric为0 */
.weui-show .weui-half-screen-dialog.extClassSpecifications {
  max-height: 90vh;
  /* border-radius: 0%; */
}

.weui-show .weui-half-screen-dialog.extClassSpecifications .weui-half-screen-dialog__hd {
  padding: 0 20rpx;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}


.radio-group-label{
  /* width: 100%; */
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 20rpx;
  font-weight: bold;
}

.iconwechat_pay{
  color: #09bb07;
  font-size:40rpx;
}

.iconyue{
  color: #f0dcab;
  font-size:40rpx;
}
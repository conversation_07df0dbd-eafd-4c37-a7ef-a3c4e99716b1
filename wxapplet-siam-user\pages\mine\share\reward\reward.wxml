<view class="balance-views theme-bg">
  <view class="balance-text">
    当前奖励金额（元）<text class="iconfont iconwenhao" bindtap="selectCurrent"></text>
  </view>
  <view class="balance-view">
    {{userInfo.inviteRewardAmount}}
  </view>
  <view wx:if="{{userInfo.unreceivedInviteRewardAmount>0}}" bindtap="bindUnreceivedInviteReward">未到账余额：{{userInfo.unreceivedInviteRewardAmount}} 元</view>
  <view class="balance-detail">
    <view class="balance-detail-info">
      <view class="balance-quota">￥{{userInfo.yesterdayGainInviteRewardAmount}}</view>
      <view class="balance-title">昨日收益</view>
    </view>
    <view class="balance-detail-info">
      
      <button size="mini" type="primary" class="withdrawal-button" bindtap="bindImmediatelyWithdrawal">立即提现</button>
    </view>
    <view class="balance-detail-info">
      <view class="balance-quota">￥{{userInfo.totalWithdrawInviteRewardAmount}}</view>
      <view class="balance-title">累计已提</view>
    </view>
  </view>
</view>
<swiper current="{{currentTab}}" class="swiper-box" duration="300" style="height:{{winHeight-10}}px"
  bindchange="bindSlideChange">
  <swiper-item class="swiper-items">
    <scroll-view bindscrolltoupper="onPullDownRefresh" upper-threshold="-50" bindscrolltolower="onReachBottom"
      lower-threshold="10" scroll-y class='scroll-views'>
      <view class='integral-items' wx:for="{{list}}" wx:key="index" bindtap="openSpecifications" data-id="{{item.id}}">
        <view class="integral-item">
          <view class="integral-item-name">
            <text class="out_of_range one_row">{{item.message}}</text>
          </view>
          <view class="integral-item-time">
            <text>{{item.createTime}}</text>
          </view>
        </view>
        <view class="integral-item-number {{item.operateType==1?'theme-color':'color-integral'}}">
          {{item.operateType==1?"+":"-"}}{{item.number}}
        </view>
      </view>
      <is-show-tip isShow="{{list.length<=0}}" top="25"
        bottom="0" text="{{list.length<=0}}" tipText="暂无奖励" button="{{button}}"></is-show-tip>
    </scroll-view>
  </swiper-item>
</swiper>
<mp-halfScreenDialog show="{{specificationsDialog}}" extClass="extClassSpecifications">
  <view slot="title">账单详情</view>
  <view slot="desc">
    <view class="order-info-view">
        <view class="order-title">账单说明</view>
       <view class="order-info">{{recordInfo.message}}</view>
       <view class="order-title">账单金额</view>
       <view class="order-info">{{recordInfo.operateType==1?"+":"-"}}{{recordInfo.number}}元</view>
       <view class="order-title">支付时间</view>
       <view class="order-info">{{recordInfo.createTime}}</view>
       <view class="order-title" wx:if="{{recordInfo.serviceFee}}">服务费</view>
       <view class="order-info" wx:if="{{recordInfo.serviceFee}}">{{recordInfo.serviceFee}}元</view>
    </view>
  </view>
</mp-halfScreenDialog>
<mp-halfScreenDialog show="{{selectCurrentDialog}}" extClass="extClassSelectCurrent">
  <view slot="title">奖励规则</view>
  <view slot="desc">
    <view class="order-info-view">
      <scroll-view style="height: 55vh;" scroll-y>
        <text class="order-title">{{current.commissionRule}}</text>
        </scroll-view>
    </view>
  </view>
</mp-halfScreenDialog>
<form bindsubmit="formSubmit" bindreset="formReset">
  <view class="address-info-list">
    <view class="address-info-item">
      <text>联系人</text>
      <input placeholder="请填写您的姓名" name="realname"></input>
    </view>
    <view class="address-info-item">
      <text>性别</text>
      <radio-group class="sex-radio-group" bindchange="sexRadioChange" name="sex">
        <label wx:for="{{sexRadio}}" wx:key="index" class="sex-radio-group-label">
          <radio value="{{item.id}}" checked="{{item.checked}}" class="block-radio"></radio>{{item.name}}
        </label>
      </radio-group>
    </view>
    <view class="address-info-item">
      <text>手机号</text>
      <input placeholder="请输入您的手机号" type="number" name="phone" bindblur="bindPhoneBlur"></input>
    </view>
    <view class="address-info-item">
      <text>地址</text>
      <view class="section" bindtap="getRegeoAddress">
        <input placeholder="选择收货地址" disabled="disabled" name="street" value="{{street}}"></input>
        <text class="iconfont iconhtbArrowright02" style="width: auto;"></text>
      </view>
    </view>
    <view class="address-info-item">
      <text>门牌号</text>
      <input placeholder="例：5号楼203室" name="houseNumber" value="{{houseNumber}}"></input>
    </view>
    <!-- <view class="address-info-item">
      <text>标签</text>
      <radio-group class="tag-radio-group" bindchange="tagRadioChange">
        <label wx:for="{{tagRadio}}" wx:key="index" class="tag-radio-group-label {{item.checked?'tag-active theme-bg':'tag-unchecked'}}">
          <radio class="none-radio" value="{{item.id}}" checked="{{item.checked}}"/>{{item.name}}
        </label>
      </radio-group>
    </view> -->
  </view>
  <view >
    <checkbox-group name="isDefault" bindchange="isDefaultChange">
      <label class="setting-default">
        <checkbox checked="{{checkbox}}" value="{{isDefault}}"></checkbox>
        <text class="default-words">设为默认地址</text>
      </label>
    </checkbox-group>
  </view>
  <view class="operation-view">
    <button class="save-view theme-bg" hover-class="hover-class-public" form-type="submit"
    style="margin: 0;width: 100%;padding: initial;">保存</button>
  </view>
</form>
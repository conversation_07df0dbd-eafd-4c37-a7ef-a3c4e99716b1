# 支付逻辑重构说明

## 重构概述

本次重构将支付页面的金额计算逻辑从前端计算改为调用后端接口计算，提高了准确性和可维护性。

## 主要变更

### 1. 新增统一金额计算方法

在 `pages/menu/pay/pay.js` 中新增了 `calculateOrderAmount()` 方法：
- 统一调用后端接口 `/api-order/rest/member/order/calculate`
- 参数与 `/order/insert` 接口保持一致
- 返回总价、实际支付金额、优惠券折扣金额等

### 2. 重构页面初始化逻辑

- 简化了 `onLoad` 方法
- 页面加载时自动选择最优优惠券
- 调用金额计算接口获取准确金额

### 3. 重构优惠券选择逻辑

修改了 `pages/mine/coupons/coupons.js`：
- `onImmediateUse()`: 选择优惠券后调用金额计算接口
- `onRadioChange()`: 取消优惠券后重新计算金额
- 移除了前端的复杂优惠券计算逻辑

### 4. 重构地址选择逻辑

修改了 `pages/address/choose/choose.js`：
- 选择地址后直接调用金额计算接口
- 移除了前端的配送费计算逻辑
- 简化了地址选择流程

### 5. 重构门店自取切换逻辑

修改了支付页面的 `clickTab()` 方法：
- 切换配送方式后调用金额计算接口
- 移除了前端的配送费加减逻辑

## 接口参数说明

`calculateOrderAmount()` 方法调用的参数包括：
- `shopId`: 店铺ID
- `orderDetailListStr`: 订单详情JSON字符串
- `shoppingWay`: 配送方式（1=门店自取, 2=外卖配送）
- `deliveryAddressId`: 配送地址ID（外卖配送时）
- `deliveryFee`: 配送费
- `couponsId`: 优惠券ID
- `couponsMemberRelationId`: 优惠券关联ID
- `couponsDescription`: 优惠券描述
- `fullReductionRuleId`: 满减规则ID
- `fullReductionRuleDescription`: 满减规则描述
- `remark`: 备注
- `isUpstairs`: 是否上楼

## 测试要点

1. **页面初始化测试**
   - 进入支付页面时是否自动选择优惠券
   - 金额显示是否正确

2. **优惠券选择测试**
   - 选择不同优惠券时金额是否正确更新
   - 取消优惠券时金额是否正确恢复

3. **地址选择测试**
   - 选择不同地址时配送费是否正确计算
   - 金额是否正确更新

4. **配送方式切换测试**
   - 外卖配送与门店自取切换时金额是否正确
   - 配送费是否正确显示

5. **综合测试**
   - 多种操作组合时金额计算是否准确
   - 各种边界情况处理是否正确

## 注意事项

1. 确保后端接口 `/api-order/rest/member/order/calculate` 已正确实现
2. 接口返回的数据结构需要包含：
   - `totalPrice`: 总价
   - `actualPrice`: 实际支付金额
   - `discountAmount`: 优惠券折扣金额
   - `deliveryFee`: 配送费
3. 所有金额计算现在都依赖后端，前端不再进行复杂计算
4. 错误处理已加强，计算失败时会显示提示信息

## 后续优化建议

1. 可以考虑添加金额计算的缓存机制
2. 优化接口调用频率，避免频繁请求
3. 添加更详细的错误处理和用户提示
4. 考虑添加金额计算的loading状态优化用户体验

page{
  background: #f5f5f5;
}

.swiper-content {
  border-radius: 15rpx;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.swiper-tab-item {
  width: 50%;
}

.swiper-box {
  /* height: 400rpx; */
  border-radius: 0 0 15rpx 15rpx;
}

.swiper-tab{
  width: 100%;
  box-shadow: -2px 0px 5px 0.5px rgba(0, 0, 0, 0.1);
  text-align: center;
  height: 88rpx;
  line-height: 88rpx;
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  background: #fff;
  z-index: 1;
}

.swiper-tab-item{
  width: 50%;
}

.swiper-box{
  display: block; 
  width: 100%; 
  height: 100%; 
  overflow: hidden;
}

.swiper-items{
  height: 100%;
}

.scroll-views{
  height: 100%;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.isRelation{
  opacity: 0.4;
}

.coupins-item{
  background: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin: 20rpx;
}

.item-top{
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.couponsName{
  font-size: 30rpx;
  font-weight: bold;
}

.endTime{
  color: #7882c7;
  font-size: 28rpx;
  line-height: 66rpx;
  height: 66rpx;
}

.bottom-view{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
}

.usage-rule{
  font-size: 28rpx;
  color: #a9a9a9;
}

.immediate-use{
  padding: 10rpx 20rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
}

.afterDiscountPrice{
  color: red;
  font-size: 24rpx;
  text-align: right;
}

.out-of-commission{
  color: #a9a9a9;
  border: 1rpx solid #a9a9a9;
}

.couponsNum{
  text-align: right;
}